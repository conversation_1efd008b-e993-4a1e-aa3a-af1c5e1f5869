"use client";

import { useState, useEffect, useCallback, useMemo, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { format } from "date-fns";
import useS<PERSON> from "swr";
import { PageHeader } from "@/components/ui/page-header";
import { formatPatientName } from "@/lib/utils/patient-name-formatter";

// Helper function to calculate appointment height based on length
function calculateAppointmentHeight(lengthInMinutes: number) {
  // Base height for a 10-minute appointment
  const baseHeight = 30; // pixels

  // Calculate height proportionally, with a minimum height
  // Increase the minimum height to ensure all content is visible
  const calculatedHeight = Math.max(
    baseHeight * (lengthInMinutes / 10),
    120 // Increased minimum height in pixels to prevent content cutoff
  );

  console.log(`Calculated height for ${lengthInMinutes} minutes: ${calculatedHeight}px`);
  return calculatedHeight;
}

// Helper function to convert time string to minutes since midnight
function timeToMinutes(timeStr: string) {
  if (!timeStr) return 0;

  try {
    // Log the time string for debugging
    console.log(`Parsing time string: "${timeStr}"`);

    // Handle the common "00:00" case from Sikka API (midnight) - convert to reasonable default
    if (timeStr === "00:00" || timeStr === "0:00") {
      console.log(`Converting midnight time to 8:00 AM default`);
      return 8 * 60; // 8:00 AM in minutes
    }

    // Try to parse the time string with various formats
    let hours = 0;
    let minutes = 0;

    // Format: "HH:MM AM/PM" or "H:MM AM/PM"
    const timeFormat1 = timeStr.match(/(\d+):(\d+)\s*(AM|PM|am|pm)?/i);

    // Format: "HH:MM" (24-hour)
    const timeFormat2 = timeStr.match(/^(\d+):(\d+)$/);

    // Format: "H AM/PM" or "HH AM/PM" (no minutes)
    const timeFormat3 = timeStr.match(/(\d+)\s*(AM|PM|am|pm)/i);

    // Format: "HHMM" (military time without colon)
    const timeFormat4 = timeStr.match(/^(\d{2})(\d{2})$/);

    if (timeFormat1) {
      // Format 1: "HH:MM AM/PM"
      hours = parseInt(timeFormat1[1], 10);
      minutes = parseInt(timeFormat1[2], 10);
      const period = timeFormat1[3] ? timeFormat1[3].toUpperCase() : null;

      console.log(`Parsed format 1: ${hours}:${minutes} ${period || ''}`);

      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }
    }
    else if (timeFormat2) {
      // Format 2: "HH:MM" (24-hour)
      hours = parseInt(timeFormat2[1], 10);
      minutes = parseInt(timeFormat2[2], 10);
      console.log(`Parsed format 2: ${hours}:${minutes}`);
    }
    else if (timeFormat3) {
      // Format 3: "H AM/PM" (no minutes)
      hours = parseInt(timeFormat3[1], 10);
      const period = timeFormat3[2].toUpperCase();
      console.log(`Parsed format 3: ${hours}:00 ${period}`);

      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }
    }
    else if (timeFormat4) {
      // Format 4: "HHMM" (military time without colon)
      hours = parseInt(timeFormat4[1], 10);
      minutes = parseInt(timeFormat4[2], 10);
      console.log(`Parsed format 4: ${hours}:${minutes}`);
    }
    else {
      // If no format matches, try to handle special cases
      if (timeStr.toLowerCase() === "noon") {
        hours = 12;
        minutes = 0;
        console.log(`Parsed special case: noon = 12:00`);
      } else if (timeStr.toLowerCase() === "midnight") {
        hours = 0;
        minutes = 0;
        console.log(`Parsed special case: midnight = 00:00`);
      } else {
        // If still no match, return 8 AM as default (480 minutes)
        console.error("Unrecognized time format:", timeStr, "defaulting to 8:00 AM");
        return 8 * 60;
      }
    }

    const totalMinutes = hours * 60 + minutes;
    console.log(`Converted to ${totalMinutes} minutes since midnight`);
    return totalMinutes;
  } catch (e) {
    console.error("Time parsing error:", e, timeStr, "defaulting to 8:00 AM");
    return 8 * 60;
  }
}

// Helper function to format minutes since midnight to a time string
function minutesToTime(minutes: number) {
  let hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  let period = 'AM';

  if (hours >= 12) {
    period = 'PM';
    if (hours > 12) hours -= 12;
  }

  if (hours === 0) hours = 12;

  return `${hours}:${mins.toString().padStart(2, '0')} ${period}`;
}

// Helper function to format time
function formatTimeDisplay(timeStr: string) {
  // Check if the time is already in a good format
  if (!timeStr) return '';

  try {
    // Convert to minutes and back to ensure consistent formatting
    const minutes = timeToMinutes(timeStr);
    return minutesToTime(minutes);
  } catch (e) {
    // If parsing fails, return the original string
    console.error("Time formatting error:", e, timeStr);
    return timeStr;
  }
}

// Simple component for appointment card
function AppointmentCard({ appointment, onClick }: { appointment: any, onClick: (appointment: any) => void }) {
  try {
    // Safety check for appointment object
    if (!appointment || typeof appointment !== 'object') {
      console.error('Invalid appointment object:', appointment);
      return null; // Return null immediately for invalid input
    }

    // Simplified version for debugging
    const appointmentId = appointment.id || 'N/A';
    const patientName = String(appointment.patient_name || appointment.patientName || "No Patient");

    // Handle click event safely
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      if (typeof onClick === 'function') {
        onClick(appointment);
      }
    };

    // Return minimal JSX outside the try block
    return (
      <div
        className="bg-gray-200 dark:bg-gray-700 p-2 rounded mb-2"
        onClick={handleClick}
      >
        Simplified Appointment Card: {patientName} (ID: {appointmentId})
      </div>
    );

  } catch (error) {
    console.error('Error rendering appointment card:', error);
    return null; // Return null if an error occurs during rendering
  }
}

// Helper function to safely sanitize an appointment object
function sanitizeAppointment(appt: any, operatory: string) {
  try {
    // Check if the appointment is a valid object
    if (!appt || typeof appt !== 'object') {
      console.error('Invalid appointment object:', appt);
      return null;
    }

    // Create a new object with only the properties we need
    return {
      id: String(appt.id || `${operatory}-${Math.random().toString(36).substring(2, 9)}`),
      appointment_sr_no: appt.appointment_sr_no ? String(appt.appointment_sr_no) : undefined,
      patient_name: String(appt.patient_name || appt.patientName || "No Patient"),
      patientName: String(appt.patient_name || appt.patientName || "No Patient"),
      startTime: String(appt.startTime || appt.appointment_time || "8:00 AM"),
      endTime: String(appt.endTime || "9:00 AM"),
      length: Number(appt.length || 60),
      reason: String(appt.appointment_type || appt.type || appt.procedure || appt.description || "Unknown"),
      type: String(appt.appointment_type || appt.type || appt.procedure || appt.description || "Unknown"),
      description: String(appt.description || ""),
      notes: String(appt.notes || ""),
      operatory: String(appt.operatory || operatory),
      patient_id: appt.patient_id ? String(appt.patient_id) : (appt.patientId ? String(appt.patientId) : ""),
      provider: (() => {
        try {
          if (appt.provider) {
            // Handle case where provider might be an object
            if (typeof appt.provider === 'object') {
              return appt.provider.name || JSON.stringify(appt.provider);
            } else {
              return String(appt.provider);
            }
          } else if (appt.providerName) {
            // Handle case where providerName might be an object
            if (typeof appt.providerName === 'object') {
              return appt.providerName.name || JSON.stringify(appt.providerName);
            } else {
              return String(appt.providerName);
            }
          }
          return "";
        } catch (error) {
          console.error('Error processing provider in sanitizeAppointment:', error);
          return "";
        }
      })(),
      status: appt.status ? String(appt.status) : "",
      isBlocked: Boolean(appt.isBlocked)
    };
  } catch (error) {
    console.error('Error sanitizing appointment:', error);
    return null;
  }
}

// Time slot component for the grid
function TimeSlot({ time, isHour }: { time: string, isHour: boolean }) {
  return (
    <div className={`h-6 border-b border-gray-400 dark:border-gray-500 flex items-center text-xs text-gray-600 dark:text-gray-300 ${isHour ? 'border-b-2 font-medium border-gray-500 dark:border-gray-400' : ''}`}>
      {isHour && (
        <span className="w-16 text-right pr-2">{time}</span>
      )}
    </div>
  );
}

// Rewritten operatory column with exact spreadsheet-like positioning
function OperatoryColumn({ operatory, appointments, onAppointmentClick, onPatientClick, displayStartHour, displayEndHour }: {
  operatory: string,
  appointments: any[],
  onAppointmentClick: (appointment: any) => void,
  onPatientClick?: (appointment: any, e: React.MouseEvent) => void,
  displayStartHour: number,
  displayEndHour: number
}) {
  // Get and sanitize appointments for this operatory
  const operatoryAppointments = appointments
    .filter(appt => appt && appt.operatory === operatory)
    .map(appt => sanitizeAppointment(appt, operatory))
    .filter(Boolean);

  // Calculate time slots (each 10-minute slot = 24px)
  const timeSlots = [];
  for (let hour = displayStartHour; hour <= displayEndHour; hour++) {
    for (let minute = 0; minute < 60; minute += 10) {
      timeSlots.push({
        isHour: minute === 0,
        slotIndex: ((hour - displayStartHour) * 6) + (minute / 10)
      });
    }
  }

  // Process appointments with exact positioning and prevent false overlaps
  const processedAppointments = operatoryAppointments
    .map((appointment, index) => {
      const startMinutes = timeToMinutes(appointment.startTime);
      const duration = Number(appointment.length) || 60;
      const topPosition = ((startMinutes - (displayStartHour * 60)) / 10) * 24;
      const height = (duration / 10) * 24;
      const endMinutes = startMinutes + duration;

      return {
        ...appointment,
        topPosition,
        height,
        startMinutes,
        endMinutes,
        index
      };
    })
    // Sort by start time to ensure correct stacking
    .sort((a, b) => a.startMinutes - b.startMinutes);

  // Mark true overlaps (double-bookings) only
  processedAppointments.forEach((appt, i) => {
    appt.hasOverlap = false;
    for (let j = 0; j < i; j++) {
      const prev = processedAppointments[j];
      if (appt.startMinutes < prev.endMinutes && appt.endMinutes > prev.startMinutes) {
        appt.hasOverlap = true;
        break;
      }
    }
  });

  return (
    <div className="min-w-0 h-full bg-gray-300 dark:bg-gray-700">
      <div className="relative" style={{ height: `${timeSlots.length * 24}px` }}>
        {/* Grid lines */}
        {timeSlots.map((slot) => (
          <div
            key={`grid-${slot.slotIndex}`}
            className="absolute left-0 right-0 pointer-events-none"
            style={{
              top: `${slot.slotIndex * 24}px`,
              height: '24px',
              borderTop: slot.isHour ? '2px solid #4b5563' : '1px solid #6b7280'
            }}
          />
        ))}

        {/* Appointments */}
        {processedAppointments.map((appointment) => {
          if (!isAppointment(appointment)) {
            return (
              <div
                key={appointment.id}
                style={{
                  position: 'absolute',
                  top: `${appointment.topPosition}px`,
                  left: '0',
                  width: '100%',
                  height: `${appointment.height}px`,
                  zIndex: 10 + appointment.index
                }}
              >
                <ScheduleNote note={appointment} onClick={onAppointmentClick} />
              </div>
            );
          }

          return (
            <div
              key={appointment.id}
              style={{
                position: 'absolute',
                top: `${appointment.topPosition}px`,
                left: appointment.hasOverlap ? '50%' : '0',
                width: appointment.hasOverlap ? '50%' : '100%',
                height: `${appointment.height}px`,
                zIndex: 10 + appointment.index
              }}
            >
              <div
                className={`h-full w-full bg-gray-50 dark:bg-gray-600 rounded cursor-pointer border-l-4 text-xs border border-gray-700 dark:border-gray-300 ${getProviderBorderColor(appointment)}`}
                onClick={() => onAppointmentClick(appointment)}
                style={{
                  padding: '2px 4px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-start',
                  overflow: 'hidden',
                  boxSizing: 'border-box'
                }}
              >
                {/* Mobile-first responsive layout */}
                <div className="w-full">
                  {/* Mobile layout: Stack name and time vertically */}
                  <div className="sm:hidden">
                    <div className="flex items-center justify-between mb-1">
                      <div
                        className="font-semibold text-blue-600 dark:text-blue-400 text-xs leading-tight cursor-pointer hover:text-blue-800 dark:hover:text-blue-300 hover:underline flex-1 transition-colors duration-200"
                        onClick={(e) => onPatientClick?.(appointment, e)}
                        title="View patient details"
                      >
                        {formatPatientName(appointment.patient_name)}
                      </div>
                      <button
                        className="text-xs text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 cursor-pointer ml-2 px-2 py-1 rounded font-medium shadow-sm hover:shadow-md transition-all duration-200 border border-blue-700 dark:border-blue-400"
                        onClick={(e) => {
                          e.stopPropagation();
                          onAppointmentClick(appointment);
                        }}
                        title="View appointment details"
                      >
                        Appt
                      </button>
                    </div>
                    <div className="flex items-center justify-between mb-1">
                      {getAgeGender(appointment) && (
                        <span className="text-gray-600 dark:text-gray-400 text-xs">
                          {getAgeGender(appointment)}
                        </span>
                      )}
                      <div className="text-gray-600 dark:text-gray-400 font-medium text-xs">
                        {formatTimeDisplay(appointment.startTime)}
                      </div>
                    </div>
                    {/* Mobile appointment type */}
                    {appointment.type && (
                      <div className="flex items-center justify-between">
                        <div className="text-gray-600 dark:text-gray-300 text-xs truncate flex-1 mr-2">
                          {appointment.type}
                        </div>
                        {/* Mobile Overjet Link */}
                        {appointment.patient_id &&
                          appointment.patient_name !== "No Patient" &&
                          appointment.patient_name !== "Unknown Patient" && (
                            <a
                              href={`https://clinic.overjet.ai/app/fmx/dailypatients/${appointment.patient_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 text-xs font-bold tracking-wide transition-colors flex-shrink-0 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30 px-1 py-0.5 rounded"
                              onClick={(e) => e.stopPropagation()}
                              title="View patient X-rays in Overjet AI"
                            >
                              Overjet
                            </a>
                          )}
                      </div>
                    )}
                  </div>

                  {/* Desktop layout: Original horizontal layout */}
                  <div className="hidden sm:flex items-center justify-between w-full">
                    <div className="flex items-center gap-1 flex-1 min-w-0">
                      <span
                        className="font-semibold text-blue-600 dark:text-blue-400 truncate cursor-pointer hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors duration-200"
                        onClick={(e) => onPatientClick?.(appointment, e)}
                        title="View patient details"
                      >
                        {formatPatientName(appointment.patient_name)}
                      </span>
                      {getAgeGender(appointment) && (
                        <span className="text-gray-600 dark:text-gray-400 text-xs">
                          {getAgeGender(appointment)}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-gray-600 dark:text-gray-400 font-medium text-xs">
                        {formatTimeDisplay(appointment.startTime)}
                      </div>
                      <button
                        className="text-xs text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 cursor-pointer px-2 py-1 rounded font-medium shadow-sm hover:shadow-md transition-all duration-200 border border-blue-700 dark:border-blue-400"
                        onClick={(e) => {
                          e.stopPropagation();
                          onAppointmentClick(appointment);
                        }}
                        title="View appointment details"
                      >
                        Appt
                      </button>
                    </div>
                  </div>
                </div>
                {/* Desktop appointment type - hidden on mobile since it's included in mobile layout above */}
                {appointment.type && (
                  <div className="hidden sm:flex mt-1 items-center justify-between">
                    <div className="text-gray-600 dark:text-gray-300 text-xs truncate flex-1 mr-2">
                      {appointment.type}
                    </div>
                    {/* Desktop Overjet Link */}
                    {appointment.patient_id &&
                      appointment.patient_name !== "No Patient" &&
                      appointment.patient_name !== "Unknown Patient" && (
                        <a
                          href={`https://clinic.overjet.ai/app/fmx/dailypatients/${appointment.patient_id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 text-xs font-bold tracking-wide transition-colors flex-shrink-0 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30 px-2 py-1 rounded"
                          onClick={(e) => e.stopPropagation()}
                          title="View patient X-rays in Overjet AI"
                        >
                          Overjet
                        </a>
                      )}
                  </div>
                )}

              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Helper functions for the rewritten schedule
function getProviderBorderColor(appointment: any): string {
  let providerCode = '';
  if (appointment.provider && typeof appointment.provider === 'object') {
    const href = String(appointment.provider.href || '');
    const parts = href.split('/');
    providerCode = parts[parts.length - 1].replace(/[\"'\s]+$/, '');
  } else if (appointment.provider) {
    providerCode = String(appointment.provider);
  }

  if (providerCode.startsWith('LL') || providerCode.startsWith('DL')) {
    return 'border-l-purple-400';
  } else if (providerCode.startsWith('GO') || providerCode.includes('GO')) {
    return 'border-l-green-500';
  } else if (providerCode.startsWith('DAZ') || providerCode.includes('DAZ') || providerCode.includes('DZ')) {
    return 'border-l-blue-300';
  } else if (providerCode.startsWith('NS') || providerCode.includes('NS')) {
    return 'border-l-orange-300';
  } else if (providerCode === 'XOFF') {
    return 'border-l-gray-400';
  }
  return 'border-l-blue-500';
}

function getAgeGender(appointment: any): string {
  const age = appointment.age || '';
  const gender = (appointment.gender || '').charAt(0).toUpperCase() || '';
  return age && gender ? `${age}${gender}` : '';
}

// Enhanced Schedule Timeline Component - Multi-column side-by-side operatory layout
function EnhancedScheduleTimeline({ operatories, appointments, onAppointmentClick, onPatientClick }: {
  operatories: string[],
  appointments: any[],
  onAppointmentClick: (appointment: any) => void,
  onPatientClick?: (appointment: any, e: React.MouseEvent) => void
}) {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [currentOperatoryPage, setCurrentOperatoryPage] = useState(0);

  useEffect(() => {
    setIsMounted(true);
    const checkDarkMode = () => {
      const dark = document.documentElement.classList.contains('dark');
      setIsDarkMode(dark);
    };
    checkDarkMode();
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
    return () => observer.disconnect();
  }, []);

  // Filter operatories that have appointments
  const operatoriesWithAppointments = useMemo(() => {
    return operatories.filter(operatory => {
      return appointments.some(appt => appt && appt.operatory === operatory);
    });
  }, [operatories, appointments]);

  // Mobile pagination: 2 operatories per page
  const operatoriesPerPage = 2;
  const totalPages = Math.ceil(operatoriesWithAppointments.length / operatoriesPerPage);

  // Get current operatories to display (mobile: paginated, desktop: all)
  const displayOperatories = useMemo(() => {
    if (typeof window !== 'undefined' && window.innerWidth < 640) { // sm breakpoint
      const startIndex = currentOperatoryPage * operatoriesPerPage;
      return operatoriesWithAppointments.slice(startIndex, startIndex + operatoriesPerPage);
    }
    return operatoriesWithAppointments;
  }, [operatoriesWithAppointments, currentOperatoryPage]);

  // Navigation functions
  const goToPreviousPage = () => {
    setCurrentOperatoryPage(prev => Math.max(0, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentOperatoryPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const { displayStartHour, displayEndHour } = useMemo(() => {
    // Start with default hours (8 AM to 5 PM)
    let minHour = 8;
    let maxHour = 17; // 5 PM

    if (appointments && appointments.length > 0) {
      // First pass: find the earliest start time and latest end time
      appointments.forEach(appt => {
        if (isAppointment(appt)) {
          const startMins = timeToMinutes(appt.startTime);
          const endMins = timeToMinutes(appt.endTime);

          if (startMins > 0) {
            const startHour = Math.floor(startMins / 60);
            minHour = Math.min(minHour, startHour);
          }

          if (endMins > 0) {
            const endHour = Math.ceil(endMins / 60);
            maxHour = Math.max(maxHour, endHour);
          }
        }
      });

      // Ensure we have some padding around the appointments
      minHour = Math.max(0, minHour - 1); // Add 1 hour buffer before first appointment
      maxHour = Math.min(23, maxHour + 1); // Add 1 hour buffer after last appointment

      // Ensure we have at least 8 hours of visibility
      const totalHours = maxHour - minHour;
      if (totalHours < 8) {
        const neededPadding = 8 - totalHours;
        const padStart = Math.floor(neededPadding / 2);
        const padEnd = Math.ceil(neededPadding / 2);

        minHour = Math.max(0, minHour - padStart);
        maxHour = Math.min(23, maxHour + padEnd);
      }
    }

    // Ensure we have reasonable defaults if no appointments
    if (minHour >= maxHour) {
      minHour = 8;
      maxHour = 17;
    }

    return {
      displayStartHour: minHour,
      displayEndHour: maxHour
    };
  }, [appointments]);

  const timeLabels = useMemo(() => {
    const labels = [];
    for (let hour = displayStartHour; hour <= displayEndHour; hour++) {
      for (let minute = 0; minute < 60; minute += 10) {
        const isHour = minute === 0;
        const displayTime = isHour ? formatTimeDisplay(`${hour}:00`) : '';
        labels.push({
          time: displayTime,
          isHour,
          // slotIndex is now relative to the dynamic displayStartHour
          slotIndex: ((hour - displayStartHour) * 6) + (minute / 10)
        });
      }
    }
    return labels;
  }, [displayStartHour, displayEndHour]);

  return (
    <div className="space-y-4">
      {/* Mobile Navigation Controls */}
      {totalPages > 1 && (
        <div className="sm:hidden flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 shadow">
          <button
            onClick={goToPreviousPage}
            disabled={currentOperatoryPage === 0}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${currentOperatoryPage === 0
              ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
              : 'text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {displayOperatories.map(op => op).join(', ')}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-500">
              ({currentOperatoryPage + 1} of {totalPages})
            </span>
          </div>

          <button
            onClick={goToNextPage}
            disabled={currentOperatoryPage === totalPages - 1}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${currentOperatoryPage === totalPages - 1
              ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
              : 'text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}
          >
            Next
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      )}

      {/* Column Headers */}
      <div className="flex border-b-2 border-gray-600 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-t-lg">
        {/* Time Column Header */}
        <div className="flex items-center justify-center" style={{ width: '4rem', borderRight: '2px solid #4b5563' }}>
          <span className="text-xs font-bold text-gray-700 dark:text-gray-300">Time</span>
        </div>

        {/* Operatory Headers */}
        {displayOperatories.map((operatory, index) => (
          <div
            key={`header-${operatory}`}
            className="flex-1 flex items-center justify-center py-2 px-1"
            style={{
              borderRight: index < displayOperatories.length - 1 ? '1px solid #6b7280' : 'none'
            }}
          >
            <span className="text-sm font-bold text-gray-900 dark:text-white truncate">
              {operatory}
            </span>
          </div>
        ))}
      </div>

      {/* Schedule Timeline */}
      <div className="schedule-timeline flex h-full border-2 border-gray-600 dark:border-gray-600 border-t-0 rounded-b-lg overflow-hidden shadow-lg bg-gray-300 dark:bg-gray-700">
        {/* Time Label Column */}
        <div className="relative" style={{ width: '4rem', borderRight: '2px solid #4b5563' }}>
          {/* Shared time slot grid lines and labels */}
          {timeLabels.map((label) => {
            const textColor = isDarkMode ? '#e5e7eb' : '#374151';
            return (
              <div
                key={`time-label-${label.slotIndex}`}
                className="schedule-grid-line absolute left-0 right-0 h-6 flex items-center"
                style={{
                  top: `${label.slotIndex * 24}px`,
                  width: '100%',
                  borderTop: label.isHour ? '2px solid #4b5563' : '1px solid #6b7280',
                  fontSize: label.isHour ? '0.75rem' : undefined,
                  justifyContent: label.isHour ? 'flex-end' : undefined,
                  paddingRight: label.isHour ? '0.5rem' : undefined,
                  color: label.isHour ? textColor : undefined,
                  fontWeight: label.isHour ? 'bold' : undefined,
                  opacity: 1,
                }}
              >
                {label.isHour ? label.time : null}
              </div>
            );
          })}
        </div>

        {/* Operatory Columns */}
        {displayOperatories.map((operatory, index) => (
          <div
            key={operatory}
            className="flex-1 min-w-0 bg-gray-300 dark:bg-gray-700"
            style={{
              borderRight: index < displayOperatories.length - 1 ? '1px solid #6b7280' : 'none'
            }}
          >
            <OperatoryColumn
              operatory={operatory}
              appointments={appointments}
              onAppointmentClick={onAppointmentClick}
              onPatientClick={onPatientClick}
              displayStartHour={displayStartHour}
              displayEndHour={displayEndHour}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// Helper function to determine if an item is an appointment (visual block) or a schedule note (text-only)
function isAppointment(item: any): boolean {
  if (!item || typeof item !== 'object') return false;

  // If it's explicitly marked as blocked or a note, treat as schedule note
  if (item.isBlocked ||
    (item.type && item.type.toLowerCase().includes("note")) ||
    (item.description && item.description.toLowerCase().includes("note")) ||
    (item.patient_name && item.patient_name.toLowerCase().includes("note"))) {
    return false;
  }

  // An item is an appointment if it has a patient name (not empty or generic)
  const hasPatient = item.patient_name &&
    item.patient_name !== "No Patient" &&
    item.patient_name.trim() !== "" &&
    !item.patient_name.toLowerCase().includes("no patient");

  return hasPatient;
}

// Simple component for schedule notes (text-only display)
function ScheduleNote({ note, onClick }: { note: any, onClick: (note: any) => void }) {
  try {
    if (!note || typeof note !== 'object') {
      console.error('Invalid note object:', note);
      return null;
    }
    let noteText = '';
    if (note.description && note.description.trim() !== '') {
      noteText = String(note.description).trim();
    } else if (note.type && note.type.trim() !== '') {
      noteText = String(note.type).trim();
    } else if (note.patient_name &&
      note.patient_name.trim() !== '' &&
      note.patient_name !== 'No Patient' &&
      !note.patient_name.toLowerCase().includes('no patient')) {
      noteText = String(note.patient_name).trim();
    } else {
      noteText = 'Schedule Note';
    }
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      if (typeof onClick === 'function') {
        onClick(note);
      }
    };
    return (
      <div
        className="schedule-note-card bg-yellow-300 dark:bg-sky-900 rounded cursor-pointer border-l-3 border-yellow-700 dark:border-sky-400 shadow-sm overflow-hidden"
        onClick={handleClick}
        style={{
          minHeight: '24px',
          padding: '0.5rem' // Equivalent to p-2 in Tailwind
        }}
      >
        <div
          className="text-yellow-900 dark:text-sky-100 w-full text-xs font-medium leading-snug break-words"
          style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxHeight: '100%'
          }}
        >
          {noteText}
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error rendering schedule note:', error);
    return null;
  }
}

// Main schedule page component
function ScheduleContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [cameFromHome, setCameFromHome] = useState(true);

  // Check where we came from when the component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const referrer = document.referrer;
      setCameFromHome(!referrer.includes('operatories'));
    }
  }, []);

  const handleBackClick = () => {
    if (cameFromHome) {
      router.push('/');
    } else {
      router.back();
    }
  };

  // Get date from URL
  const dateParam = searchParams?.get("date") || "";

  // Get date from URL and ensure it's properly formatted
  let formattedDate;
  if (dateParam && dateParam.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // Use the date parameter directly if it's already in YYYY-MM-DD format
    formattedDate = dateParam;
    console.log(`Using date from URL: ${formattedDate}`);
  } else {
    // Use current date as fallback (in local timezone)
    const now = new Date();
    formattedDate = format(now, 'yyyy-MM-dd');
    console.log(`Using current date (local): ${formattedDate}`);
  }

  // For testing purposes, uncomment this line to use a specific date
  // formattedDate = "2023-05-22"; // Use a specific date for testing

  console.log('=== Schedule Page Loading ===');

  // Debug log all search params
  try {
    console.log('All URL search params:', searchParams ? Object.fromEntries(searchParams.entries()) : 'No searchParams');
  } catch (e) {
    console.error('Error logging search params:', e);
  }

  // Get operatories from URL - support both comma-separated and array formats
  let operatoryParams: string[] = [];

  try {
    // First try to get as array (operatories[]=DL01&operatories[]=DL02)
    if (searchParams?.has('operatories[]')) {
      console.log('Found operatories[] parameter');
      operatoryParams = searchParams.getAll("operatories[]");
    }
    // Then try to get as comma-separated (operatories=DL01,DL02)
    else if (searchParams?.has('operatories')) {
      console.log('Found operatories parameter');
      const operatoryString = searchParams.get('operatories') || '';
      operatoryParams = operatoryString.split(',').filter(Boolean);
    } else {
      console.log('No operatories parameter found, using defaults');
      operatoryParams = ['DL01', 'DL02'];
    }

    console.log('Raw operatory params:', operatoryParams);

    // Filter out invalid operatories
    operatoryParams = operatoryParams
      .map((op: string) => String(op).trim())
      .filter((op: string) => op && op !== 'DAZ1' && op !== 'DAZ2');

    console.log('Filtered operatories:', operatoryParams);

    // If no valid operatories, use defaults
    if (operatoryParams.length === 0) {
      console.log('No valid operatories found, using default');
      operatoryParams = ['DL01', 'DL02'];
    }
  } catch (e) {
    console.error('Error processing operatories:', e);
    operatoryParams = ['DL01', 'DL02']; // Use default on error
  }

  // Get providers from URL - support both comma-separated and array formats
  let providerParams: string[] = [];

  try {
    // First try to get as array (providers[]=Dr.%20Lowell&providers[]=Hygiene)
    if (searchParams?.has('providers[]')) {
      console.log('Found providers[] parameter');
      providerParams = searchParams.getAll("providers[]");
    }
    // Then try to get as comma-separated (providers=Dr.%20Lowell,Hygiene)
    else if (searchParams?.has('providers')) {
      console.log('Found providers parameter');
      const providerString = searchParams.get('providers') || '';
      providerParams = providerString.split(',').filter(Boolean);
    }

    console.log('Raw provider params:', providerParams);

    // Filter out invalid providers
    providerParams = providerParams
      .map((provider: string) => String(provider).trim())
      .filter((provider: string) => provider && provider !== '');

    console.log('Filtered providers:', providerParams);
  } catch (e) {
    console.error('Error processing providers:', e);
    providerParams = []; // Use empty array on error
  }

  // Sort operatories - first by doctor (DL for Dr. Lowell first), then by number
  const operatories = [...operatoryParams].sort((a: string, b: string) => {
    // Extract the doctor prefix (e.g., "DL" from "DL01")
    const aPrefix = a.match(/^[A-Z]+/)?.[0] || "";
    const bPrefix = b.match(/^[A-Z]+/)?.[0] || "";

    // Extract the number suffix (e.g., "01" from "DL01")
    const aNum = parseInt(a.replace(/^[A-Z]+/, "") || "0", 10);
    const bNum = parseInt(b.replace(/^[A-Z]+/, "") || "0", 10);

    // Sort by prefix first (DL comes before others)
    if (aPrefix === "DL" && bPrefix !== "DL") return -1;
    if (aPrefix !== "DL" && bPrefix === "DL") return 1;

    // If same prefix, sort by number
    if (aPrefix === bPrefix) return aNum - bNum;

    // Otherwise sort alphabetically by prefix
    return aPrefix.localeCompare(bPrefix);
  });

  console.log('Sorted operatories:', operatories);

  // State for selected appointment
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null);

  // Parse the date for display purposes (using local timezone)
  const [year, month, day] = formattedDate.split('-').map(Number);
  const parsedDate = new Date(year, month - 1, day, 12, 0, 0);

  // Day navigation functions
  const goToPreviousDay = () => {
    const currentDate = new Date(year, month - 1, day);
    currentDate.setDate(currentDate.getDate() - 1);
    const newDate = format(currentDate, 'yyyy-MM-dd');
    router.push(`/schedule?date=${newDate}`);
  };

  const goToNextDay = () => {
    const currentDate = new Date(year, month - 1, day);
    currentDate.setDate(currentDate.getDate() + 1);
    const newDate = format(currentDate, 'yyyy-MM-dd');
    router.push(`/schedule?date=${newDate}`);
  };

  // Redirect if no date is provided
  useEffect(() => {
    if (!dateParam) {
      const defaultDate = format(new Date(), "yyyy-MM-dd");
      router.push(`/operatories?date=${defaultDate}`);
    }
  }, [dateParam, router]);

  // Build query parameters
  const params = new URLSearchParams();
  params.append("date", formattedDate);
  operatories.forEach(op => params.append("operatories[]", op));
  providerParams.forEach(provider => params.append("providers[]", provider));
  const queryString = params.toString();

  // Create a stable key for SWR - use Sikka API for real-time data
  const swrKey = formattedDate && operatories.length > 0 ? `/api/sikka/appointments?date=${formattedDate}` : null;

  // Create a stable fetcher function
  const fetcher = useCallback(async (url: string) => {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch appointments: ${response.status}`);
    }
    const data = await response.json();
    // Sikka API returns { appointments: [...] } format
    return data.appointments || data;
  }, []);

  // Use SWR for data fetching with improved caching
  const { data: appointments = [], error, isLoading, mutate } = useSWR(
    swrKey,
    fetcher,
    {
      revalidateOnFocus: true, // Enable revalidation on focus
      revalidateOnReconnect: true, // Enable revalidation on reconnect
      dedupingInterval: 60000, // 1 minute (reduced from 5 minutes)
      refreshInterval: 0, // Don't auto-refresh
      errorRetryCount: 2,
      shouldRetryOnError: false,
    }
  );
  // Handle appointment click - navigate to appointment page
  const handleAppointmentClick = (appointment: any) => {
    console.log('Appointment data:', JSON.stringify(appointment, null, 2));

    // Only navigate to appointment page if we have a valid Sikka appointment ID
    // (appointment_sr_no), not a generated fallback ID
    if (appointment.appointment_sr_no) {
      router.push(`/appointment/${appointment.appointment_sr_no}`);
    } else {
      // For appointments without valid Sikka IDs, show details in modal
      console.log('Appointment has no valid Sikka ID, showing modal instead');
      setSelectedAppointment(appointment);
    }
  };

  // Handle patient name click - navigate directly to patient page
  const handlePatientClick = (appointment: any, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent appointment click

    if (appointment.patient_id &&
      appointment.patient_name !== "No Patient" &&
      appointment.patient_name !== "Unknown Patient") {
      // Navigate directly to patient page
      router.push(`/patient/${appointment.patient_id}`);
    }
  };

  // Close appointment details
  const handleCloseDetails = () => {
    setSelectedAppointment(null);
  };

  return (
    <div className="schedule-page min-h-screen bg-gray-50 dark:bg-gray-900">
      <PageHeader
        title="Dentalapp"
        showBackButton
        onBackClick={handleBackClick}
        backButtonLabel={cameFromHome ? 'Back to Home' : 'Back to Operatories'}
        activeTab="schedule"
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          {/* Mobile: Stack title and buttons vertically */}
          <div className="sm:hidden space-y-3 mb-2">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white text-center">
              Schedule for {parsedDate.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                timeZone: 'UTC'
              })}
            </h2>

            {/* Day Navigation Buttons - Mobile */}
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={goToPreviousDay}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors"
                title="Previous Day"
              >
                ← Prev
              </button>
              <button
                onClick={() => router.push(`/voice-workflow-schedule?date=${formattedDate}`)}
                className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-md transition-colors"
                title="Voice Workflow"
              >
                🎤 Voice
              </button>
              <button
                onClick={goToNextDay}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors"
                title="Next Day"
              >
                Next →
              </button>
            </div>
          </div>

          {/* Desktop: Side by side layout */}
          <div className="hidden sm:flex items-center justify-between mb-2">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Schedule for {parsedDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                timeZone: 'UTC' // Ensure consistent display
              })}
            </h2>

            {/* Day Navigation Buttons - Desktop */}
            <div className="flex items-center space-x-2">
              <button
                onClick={goToPreviousDay}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors"
                title="Previous Day"
              >
                ← Prev
              </button>
              <button
                onClick={() => router.push(`/voice-workflow-schedule?date=${formattedDate}`)}
                className="flex items-center space-x-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-md transition-colors"
                title="Voice Workflow"
              >
                <span>🎤</span>
                <span>Voice Workflow</span>
              </button>
              <button
                onClick={goToNextDay}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors"
                title="Next Day"
              >
                Next →
              </button>
            </div>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Operatories: {operatories.join(", ")}
            {providerParams.length > 0 && (
              <>
                <br />
                Providers: {providerParams.join(", ")}
              </>
            )}
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error?.message || "Failed to fetch appointments"}</span>
          </div>
        ) : !appointments || appointments.length === 0 ? (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">No appointments found </strong>
            <span className="block sm:inline">for the selected date and operatories.</span>
          </div>) : (
          <EnhancedScheduleTimeline
            operatories={operatories}
            appointments={appointments}
            onAppointmentClick={handleAppointmentClick}
            onPatientClick={handlePatientClick}
          />
        )}
      </main>
    </div>
  );
}

export default function SchedulePage() {
  return (
    <Suspense fallback={<div>Loading schedule...</div>}>
      <ScheduleContent />
    </Suspense>
  );
}

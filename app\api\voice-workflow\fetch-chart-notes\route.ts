import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

/**
 * Fetch Chart Notes from Sikka API
 * 
 * This endpoint fetches clinical notes from Sikka for specific patients/appointments
 * as part of the voice workflow process.
 * 
 * POST /api/voice-workflow/fetch-chart-notes
 */
export async function POST(request: NextRequest) {
  try {
    const { 
      patient_id, 
      appointment_id, 
      date,
      date_range = 30 // days to look back for notes
    } = await request.json();

    if (!patient_id && !appointment_id) {
      return NextResponse.json(
        { error: 'Either patient_id or appointment_id is required' },
        { status: 400 }
      );
    }

    console.log(`📋 Fetching chart notes for patient ${patient_id || 'from appointment ' + appointment_id}`);

    const sikkaClient = new SikkaApiClient(loadCredentials());
    await sikkaClient.authenticate();

    let chartNotes = [];
    let patientInfo = null;

    if (patient_id) {
      // Fetch notes directly by patient ID
      try {
        chartNotes = await sikkaClient.getClinicalNotes(patient_id, date, date_range);
        
        // Also get patient info for context
        try {
          patientInfo = await sikkaClient.getPatient(patient_id);
        } catch (error) {
          console.warn(`Could not fetch patient info for ${patient_id}:`, error);
        }

      } catch (error) {
        console.error(`Error fetching chart notes for patient ${patient_id}:`, error);
        throw new Error(`Failed to fetch chart notes for patient ${patient_id}`);
      }

    } else if (appointment_id) {
      // Fetch appointment details first, then get notes by patient ID
      try {
        const appointment = await sikkaClient.getAppointment(appointment_id);
        if (!appointment || !appointment.patient_id) {
          throw new Error(`Appointment ${appointment_id} not found or missing patient ID`);
        }

        chartNotes = await sikkaClient.getClinicalNotes(
          appointment.patient_id, 
          appointment.appointment_date || date,
          date_range
        );

        patientInfo = {
          patient_id: appointment.patient_id,
          patient_name: appointment.patient_name,
          appointment_date: appointment.appointment_date,
          appointment_time: appointment.appointment_time
        };

      } catch (error) {
        console.error(`Error fetching chart notes for appointment ${appointment_id}:`, error);
        throw new Error(`Failed to fetch chart notes for appointment ${appointment_id}`);
      }
    }

    // Process and format the chart notes
    const processedNotes = chartNotes.map((note: any) => ({
      note_id: note.id || note.note_id,
      note_date: note.date || note.note_date,
      note_time: note.time || note.note_time,
      note_text: note.note_text || note.text || note.content,
      note_type: note.type || note.note_type || 'clinical',
      provider: note.provider || note.provider_name,
      created_at: note.created_at || note.date_created,
      updated_at: note.updated_at || note.date_modified
    }));

    // Combine all notes into a single text block for easy processing
    const combinedNotes = processedNotes
      .map(note => `[${note.note_date} ${note.note_time || ''}] ${note.note_text}`)
      .join('\n\n');

    const result = {
      patient_id: patient_id || patientInfo?.patient_id,
      patient_name: patientInfo?.patient_name,
      appointment_id,
      date,
      date_range,
      notes_count: processedNotes.length,
      individual_notes: processedNotes,
      combined_notes: combinedNotes,
      fetched_at: new Date().toISOString()
    };

    console.log(`✅ Fetched ${processedNotes.length} chart notes for patient ${result.patient_id}`);

    return NextResponse.json({
      success: true,
      ...result,
      message: `Successfully fetched ${processedNotes.length} chart notes`
    });

  } catch (error) {
    console.error('❌ Error fetching chart notes:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to fetch chart notes from Sikka'
    }, { status: 500 });
  }
}

/**
 * Batch Fetch Chart Notes for Multiple Patients/Appointments
 * 
 * POST /api/voice-workflow/fetch-chart-notes/batch
 */
export async function PUT(request: NextRequest) {
  try {
    const { 
      requests, // Array of { patient_id?, appointment_id?, date? }
      date_range = 30 
    } = await request.json();

    if (!requests || !Array.isArray(requests)) {
      return NextResponse.json(
        { error: 'Requests array is required' },
        { status: 400 }
      );
    }

    console.log(`📋 Batch fetching chart notes for ${requests.length} requests`);

    const sikkaClient = new SikkaApiClient(loadCredentials());
    await sikkaClient.authenticate();

    const results = {
      successful: 0,
      failed: 0,
      notes: [] as any[],
      errors: [] as any[]
    };

    for (const req of requests) {
      try {
        const { patient_id, appointment_id, date } = req;

        if (!patient_id && !appointment_id) {
          results.failed++;
          results.errors.push({
            request: req,
            error: 'Either patient_id or appointment_id is required'
          });
          continue;
        }

        let chartNotes = [];
        let targetPatientId = patient_id;

        if (!targetPatientId && appointment_id) {
          // Get patient ID from appointment
          const appointment = await sikkaClient.getAppointment(appointment_id);
          targetPatientId = appointment?.patient_id;
        }

        if (targetPatientId) {
          chartNotes = await sikkaClient.getClinicalNotes(targetPatientId, date, date_range);
        }

        const processedNotes = chartNotes.map((note: any) => ({
          note_id: note.id || note.note_id,
          note_date: note.date || note.note_date,
          note_text: note.note_text || note.text || note.content,
          note_type: note.type || note.note_type || 'clinical',
          provider: note.provider || note.provider_name
        }));

        results.successful++;
        results.notes.push({
          patient_id: targetPatientId,
          appointment_id,
          date,
          notes_count: processedNotes.length,
          notes: processedNotes,
          combined_notes: processedNotes
            .map(note => `[${note.note_date}] ${note.note_text}`)
            .join('\n\n'),
          fetched_at: new Date().toISOString()
        });

      } catch (error) {
        results.failed++;
        results.errors.push({
          request: req,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ Batch fetch completed: ${results.successful} successful, ${results.failed} failed`);

    return NextResponse.json({
      success: true,
      summary: {
        total: requests.length,
        successful: results.successful,
        failed: results.failed
      },
      notes: results.notes,
      errors: results.errors,
      message: `Batch fetch completed: ${results.successful}/${requests.length} successful`
    });

  } catch (error) {
    console.error('❌ Error in batch chart notes fetch:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to batch fetch chart notes'
    }, { status: 500 });
  }
}

/**
 * Get Chart Notes Status/Cache
 * 
 * GET /api/voice-workflow/fetch-chart-notes?patient_id=xxx&date=YYYY-MM-DD
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const patientId = searchParams.get('patient_id');
    const appointmentId = searchParams.get('appointment_id');
    const date = searchParams.get('date');

    if (!patientId && !appointmentId) {
      return NextResponse.json(
        { error: 'Either patient_id or appointment_id is required' },
        { status: 400 }
      );
    }

    // This would typically check if we have cached chart notes
    // and return their status/freshness
    
    const cacheStatus = {
      patient_id: patientId,
      appointment_id: appointmentId,
      date,
      has_cached_notes: false, // Would check actual cache
      last_fetched: null,
      cache_age_minutes: null,
      needs_refresh: true,
      status: 'not_cached'
    };

    return NextResponse.json({
      success: true,
      cache_status: cacheStatus,
      message: 'Chart notes cache status retrieved'
    });

  } catch (error) {
    console.error('❌ Error checking chart notes status:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

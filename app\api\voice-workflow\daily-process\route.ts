import { NextRequest, NextResponse } from 'next/server';
import { VoiceWorkflowService } from '@/lib/voice-workflow-service';

/**
 * Daily Voice Workflow Processing Endpoint
 * 
 * This endpoint implements the core voice workflow described in the user's requirements:
 * 1. Match daily recordings to appointments
 * 2. Pull ALL Sikka clinical notes for the day
 * 3. Store both transcriptions and chart notes in working memory
 * 4. Prepare for professionalized note generation
 * 
 * POST /api/voice-workflow/daily-process
 * Body: { date: "YYYY-MM-DD" }
 */
export async function POST(request: NextRequest) {
  try {
    const { date } = await request.json();

    if (!date) {
      return NextResponse.json(
        { error: 'Date is required in YYYY-MM-DD format' },
        { status: 400 }
      );
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    console.log(`🔄 Starting daily voice workflow for ${date}`);

    const workflowService = new VoiceWorkflowService();
    const result = await workflowService.processDailyWorkflow(date);

    console.log(`✅ Daily workflow completed for ${date}:`, {
      total_appointments: result.summary.total_appointments,
      appointments_with_recordings: result.summary.appointments_with_recordings,
      unmatched_recordings: result.summary.unmatched_recordings
    });

    return NextResponse.json({
      success: true,
      date,
      summary: result.summary,
      appointments: result.appointments,
      unmatched_recordings: result.unmatched_recordings,
      message: `Daily workflow processed for ${date}`
    });

  } catch (error) {
    console.error('❌ Daily workflow error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to process daily workflow'
    }, { status: 500 });
  }
}

/**
 * Get Daily Workflow Status
 * 
 * GET /api/voice-workflow/daily-process?date=YYYY-MM-DD
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }

    const workflowService = new VoiceWorkflowService();
    const workingMemoryEntries = workflowService.getAllWorkingMemoryEntries();
    
    // Filter entries for the requested date
    const dateEntries = workingMemoryEntries.filter(entry => 
      entry.appointment_date.startsWith(date)
    );

    const summary = {
      date,
      total_entries: dateEntries.length,
      pending: dateEntries.filter(e => e.workflow_status === 'pending').length,
      processing: dateEntries.filter(e => e.workflow_status === 'processing').length,
      completed: dateEntries.filter(e => e.workflow_status === 'completed').length,
      error: dateEntries.filter(e => e.workflow_status === 'error').length,
      with_recordings: dateEntries.filter(e => e.recording_id).length,
      with_chart_notes: dateEntries.filter(e => e.chart_notes).length,
      with_professionalized_notes: dateEntries.filter(e => e.professionalized_note).length
    };

    return NextResponse.json({
      success: true,
      date,
      summary,
      entries: dateEntries
    });

  } catch (error) {
    console.error('❌ Error getting workflow status:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

"use client";

import { Calendar, Search, Mic } from "lucide-react";
import { format } from "date-fns";

interface QuickActionsPanelProps {
  selectedDate: string;
  onNavigate: (path: string) => void;
}

export function QuickActionsPanel({ selectedDate, onNavigate }: QuickActionsPanelProps) {
  const handleDrLowellClick = () => {
    // Navigate directly to schedule page with Dr. Lowell's operatories pre-selected
    const formattedDate = selectedDate || format(new Date(), 'yyyy-MM-dd');
    // Use a simple comma-separated list for operatories
    onNavigate(`/schedule?date=${formattedDate}&operatories=DL01,DL02,DL3A`);
  };

  const handleOperatoriesClick = () => {
    onNavigate("/operatories");
  };

  const handleFullScheduleClick = () => {
    onNavigate("/schedule");
  };

  const handleVoiceWorkflowClick = () => {
    const formattedDate = selectedDate || format(new Date(), 'yyyy-MM-dd');
    onNavigate(`/voice-workflow-schedule?date=${formattedDate}`);
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
      <button
        onClick={handleDrLowellClick}
        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm"
      >
        <Calendar className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
        <span className="hidden sm:inline">Dr. Lowell's Operatories</span>
        <span className="sm:hidden">Dr. Lowell</span>
      </button>

      <button
        onClick={handleOperatoriesClick}
        className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm"
      >
        <Search className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
        <span className="hidden sm:inline">Select Operatories</span>
        <span className="sm:hidden">Operatories</span>
      </button>

      <button
        onClick={handleFullScheduleClick}
        className="bg-slate-600 hover:bg-slate-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm"
      >
        <Calendar className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
        <span className="hidden sm:inline">Full Schedule View</span>
        <span className="sm:hidden">Schedule</span>
      </button>

      <button
        onClick={handleVoiceWorkflowClick}
        className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm"
      >
        <Mic className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
        <span className="hidden sm:inline">Voice Workflow</span>
        <span className="sm:hidden">Voice</span>
      </button>
    </div>
  );
}
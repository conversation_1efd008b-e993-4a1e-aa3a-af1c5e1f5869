import { NextRequest, NextResponse } from 'next/server';
import { VoiceWorkflowService } from '@/lib/voice-workflow-service';

/**
 * Match Recordings to Appointments Endpoint
 * 
 * This endpoint handles manual and automatic matching of voice recordings
 * to specific appointments.
 * 
 * POST /api/voice-workflow/match-recordings
 */
export async function POST(request: NextRequest) {
  try {
    const { 
      recording_id, 
      appointment_id, 
      match_type = 'manual',
      confidence,
      reasoning 
    } = await request.json();

    if (!recording_id || !appointment_id) {
      return NextResponse.json(
        { error: 'Recording ID and Appointment ID are required' },
        { status: 400 }
      );
    }

    console.log(`🔗 Matching recording ${recording_id} to appointment ${appointment_id} (${match_type})`);

    // Here you would typically update your database to record the match
    // For now, we'll simulate the matching process
    
    const matchResult = {
      recording_id,
      appointment_id,
      match_type,
      confidence: confidence || (match_type === 'manual' ? 1.0 : 0.8),
      reasoning: reasoning || `${match_type} match by user`,
      matched_at: new Date().toISOString(),
      status: 'matched'
    };

    // In a real implementation, you would:
    // 1. Validate that both recording and appointment exist
    // 2. Update the recording with appointment details
    // 3. Log the match for audit purposes
    // 4. Update any working memory or cache

    console.log(`✅ Successfully matched recording to appointment`);

    return NextResponse.json({
      success: true,
      match: matchResult,
      message: `Recording ${recording_id} successfully matched to appointment ${appointment_id}`
    });

  } catch (error) {
    console.error('❌ Error matching recording to appointment:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to match recording to appointment'
    }, { status: 500 });
  }
}

/**
 * Batch Match Recordings
 * 
 * POST /api/voice-workflow/match-recordings/batch
 */
export async function PUT(request: NextRequest) {
  try {
    const { matches } = await request.json();

    if (!matches || !Array.isArray(matches)) {
      return NextResponse.json(
        { error: 'Matches array is required' },
        { status: 400 }
      );
    }

    console.log(`🔗 Processing batch match for ${matches.length} recordings`);

    const results = {
      successful: 0,
      failed: 0,
      matches: [] as any[],
      errors: [] as any[]
    };

    for (const match of matches) {
      try {
        const { recording_id, appointment_id, confidence, reasoning } = match;

        if (!recording_id || !appointment_id) {
          results.failed++;
          results.errors.push({
            recording_id,
            appointment_id,
            error: 'Missing recording_id or appointment_id'
          });
          continue;
        }

        // Process the match
        const matchResult = {
          recording_id,
          appointment_id,
          match_type: 'batch',
          confidence: confidence || 0.8,
          reasoning: reasoning || 'Batch match operation',
          matched_at: new Date().toISOString(),
          status: 'matched'
        };

        results.successful++;
        results.matches.push(matchResult);

      } catch (error) {
        results.failed++;
        results.errors.push({
          recording_id: match.recording_id,
          appointment_id: match.appointment_id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ Batch match completed: ${results.successful} successful, ${results.failed} failed`);

    return NextResponse.json({
      success: true,
      summary: {
        total: matches.length,
        successful: results.successful,
        failed: results.failed
      },
      matches: results.matches,
      errors: results.errors,
      message: `Batch match completed: ${results.successful}/${matches.length} successful`
    });

  } catch (error) {
    console.error('❌ Error in batch matching:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to process batch match'
    }, { status: 500 });
  }
}

/**
 * Unmatch Recording from Appointment
 * 
 * DELETE /api/voice-workflow/match-recordings
 */
export async function DELETE(request: NextRequest) {
  try {
    const { recording_id, reason } = await request.json();

    if (!recording_id) {
      return NextResponse.json(
        { error: 'Recording ID is required' },
        { status: 400 }
      );
    }

    console.log(`🔓 Unmatching recording ${recording_id}`);

    // Here you would typically:
    // 1. Find the current match for this recording
    // 2. Remove the match from the database
    // 3. Log the unmatch operation
    // 4. Update working memory

    const unmatchResult = {
      recording_id,
      reason: reason || 'Manual unmatch by user',
      unmatched_at: new Date().toISOString(),
      status: 'unmatched'
    };

    console.log(`✅ Successfully unmatched recording ${recording_id}`);

    return NextResponse.json({
      success: true,
      unmatch: unmatchResult,
      message: `Recording ${recording_id} successfully unmatched`
    });

  } catch (error) {
    console.error('❌ Error unmatching recording:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to unmatch recording'
    }, { status: 500 });
  }
}

/**
 * Get Match Suggestions
 * 
 * GET /api/voice-workflow/match-recordings?recording_id=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recording_id');
    const date = searchParams.get('date');

    if (!recordingId && !date) {
      return NextResponse.json(
        { error: 'Either recording_id or date is required' },
        { status: 400 }
      );
    }

    if (recordingId) {
      // Get match suggestions for a specific recording
      console.log(`🔍 Getting match suggestions for recording ${recordingId}`);

      // This would typically:
      // 1. Analyze the recording (timing, transcription content, etc.)
      // 2. Find potential appointment matches
      // 3. Score the matches based on various factors
      // 4. Return ranked suggestions

      const suggestions = [
        {
          appointment_id: 'apt_123',
          patient_name: 'John Doe',
          appointment_time: '10:00 AM',
          operatory: 'OP1',
          confidence: 0.95,
          reasoning: 'Time proximity and patient name match in transcription'
        },
        {
          appointment_id: 'apt_124',
          patient_name: 'Jane Smith',
          appointment_time: '10:30 AM',
          operatory: 'OP2',
          confidence: 0.75,
          reasoning: 'Time proximity match'
        }
      ];

      return NextResponse.json({
        success: true,
        recording_id: recordingId,
        suggestions,
        message: `Found ${suggestions.length} potential matches`
      });

    } else if (date) {
      // Get all unmatched recordings for a date
      console.log(`🔍 Getting unmatched recordings for ${date}`);

      const workflowService = new VoiceWorkflowService();
      // This would fetch unmatched recordings from the database
      const unmatchedRecordings = []; // Placeholder

      return NextResponse.json({
        success: true,
        date,
        unmatched_recordings: unmatchedRecordings,
        count: unmatchedRecordings.length,
        message: `Found ${unmatchedRecordings.length} unmatched recordings for ${date}`
      });
    }

  } catch (error) {
    console.error('❌ Error getting match suggestions:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

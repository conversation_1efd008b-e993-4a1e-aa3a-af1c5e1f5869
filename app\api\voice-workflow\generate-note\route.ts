import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Generate Professionalized Note Endpoint
 * 
 * This endpoint combines voice transcriptions and Sikka chart notes to create
 * professionalized clinical notes for dental visits.
 * 
 * POST /api/voice-workflow/generate-note
 */
export async function POST(request: NextRequest) {
  try {
    const {
      patient_name,
      appointment_date,
      appointment_time,
      operatory,
      provider,
      transcription,
      chart_notes
    } = await request.json();

    if (!patient_name || !appointment_date) {
      return NextResponse.json(
        { error: 'Patient name and appointment date are required' },
        { status: 400 }
      );
    }

    console.log(`📝 Generating professionalized note for ${patient_name} on ${appointment_date}`);

    // Prepare the prompt for AI note generation
    const prompt = createProfessionalNotePrompt({
      patient_name,
      appointment_date,
      appointment_time,
      operatory,
      provider,
      transcription,
      chart_notes
    });

    // Generate the professionalized note using OpenAI
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional dental clinical note writer. Create comprehensive, professional clinical notes that combine voice transcriptions and existing chart notes into a cohesive, well-structured clinical documentation."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1500
    });

    const professionalizedNote = completion.choices[0]?.message?.content;

    if (!professionalizedNote) {
      throw new Error('Failed to generate professionalized note');
    }

    console.log(`✅ Generated professionalized note for ${patient_name}`);

    return NextResponse.json({
      success: true,
      professionalized_note: professionalizedNote,
      metadata: {
        patient_name,
        appointment_date,
        appointment_time,
        operatory,
        provider,
        generated_at: new Date().toISOString(),
        has_transcription: !!transcription,
        has_chart_notes: !!chart_notes,
        word_count: professionalizedNote.split(' ').length
      }
    });

  } catch (error) {
    console.error('❌ Error generating professionalized note:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      message: 'Failed to generate professionalized note'
    }, { status: 500 });
  }
}

/**
 * Create the prompt for AI note generation
 */
function createProfessionalNotePrompt({
  patient_name,
  appointment_date,
  appointment_time,
  operatory,
  provider,
  transcription,
  chart_notes
}: {
  patient_name: string;
  appointment_date: string;
  appointment_time?: string;
  operatory?: string;
  provider?: string;
  transcription?: string;
  chart_notes?: string;
}): string {
  const sections = [];

  // Header information
  sections.push(`PATIENT: ${patient_name}`);
  sections.push(`DATE: ${appointment_date}`);
  if (appointment_time) sections.push(`TIME: ${appointment_time}`);
  if (operatory) sections.push(`OPERATORY: ${operatory}`);
  if (provider) sections.push(`PROVIDER: ${provider}`);
  sections.push('');

  // Instructions
  sections.push('Please create a comprehensive, professional dental clinical note by combining the following information:');
  sections.push('');

  // Voice transcription section
  if (transcription && transcription.trim()) {
    sections.push('VOICE TRANSCRIPTION FROM VISIT:');
    sections.push('---');
    sections.push(transcription.trim());
    sections.push('---');
    sections.push('');
  }

  // Existing chart notes section
  if (chart_notes && chart_notes.trim()) {
    sections.push('EXISTING CHART NOTES:');
    sections.push('---');
    sections.push(chart_notes.trim());
    sections.push('---');
    sections.push('');
  }

  // Requirements
  sections.push('REQUIREMENTS FOR THE PROFESSIONALIZED NOTE:');
  sections.push('1. Combine information from both voice transcription and existing chart notes');
  sections.push('2. Use professional dental terminology and proper clinical documentation format');
  sections.push('3. Organize information logically (Chief Complaint, Examination, Treatment, Plan, etc.)');
  sections.push('4. Remove any redundant information between the two sources');
  sections.push('5. Maintain accuracy - do not add information not present in the source materials');
  sections.push('6. Use standard dental abbreviations and terminology where appropriate');
  sections.push('7. Ensure the note is suitable for legal medical documentation');
  sections.push('8. If there are conflicts between sources, note them appropriately');
  sections.push('');

  // Special cases
  if (!transcription || !transcription.trim()) {
    sections.push('NOTE: No voice transcription available - base the note solely on existing chart notes.');
    sections.push('');
  }

  if (!chart_notes || !chart_notes.trim()) {
    sections.push('NOTE: No existing chart notes available - base the note solely on voice transcription.');
    sections.push('');
  }

  if ((!transcription || !transcription.trim()) && (!chart_notes || !chart_notes.trim())) {
    sections.push('NOTE: Neither voice transcription nor chart notes are available. Create a basic appointment note template.');
    sections.push('');
  }

  sections.push('Please provide the professionalized clinical note:');

  return sections.join('\n');
}

/**
 * Get Note Generation Status
 * 
 * GET /api/voice-workflow/generate-note?appointment_id=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('appointment_id');

    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID is required' },
        { status: 400 }
      );
    }

    // This would typically check the status of note generation
    // For now, return a placeholder response
    return NextResponse.json({
      success: true,
      appointment_id: appointmentId,
      status: 'ready_for_generation',
      message: 'Ready to generate professionalized note'
    });

  } catch (error) {
    console.error('❌ Error checking note generation status:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

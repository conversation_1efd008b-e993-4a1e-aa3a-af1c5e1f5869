/**
 * Calendar layout algorithm for handling overlapping appointments and events
 * Based on Google Calendar's layout algorithm
 */

export function layoutAppointments(items: any[]) {
  console.log('layoutAppointments called with:', items.length, 'items');
  if (!items || items.length === 0) {
    console.log('No items to layout');
    return [];
  }

  // Convert all items (appointments + events) to layout objects with time calculations
  const layoutItems = items.map((item, index) => {
    const startMinutes = timeToMinutes(item.startTime);
    const endMinutes = timeToMinutes(item.endTime) || 
                      (startMinutes + (Number(item.length) || 60));
    
    return {
      ...item,
      originalIndex: index,
      startMinutes,
      endMinutes,
      column: 0,
      maxColumns: 1,
      isEvent: !isAppointment(item) // Track if this is an event/note
    };
  });

  // Sort by start time, then by duration (longer first)
  layoutItems.sort((a, b) => {
    if (a.startMinutes !== b.startMinutes) {
      return a.startMinutes - b.startMinutes;
    }
    return (b.endMinutes - b.startMinutes) - (a.endMinutes - a.startMinutes);
  });

  // Find overlapping groups
  const groups = [];
  let currentGroup = [];

  for (let i = 0; i < layoutItems.length; i++) {
    const item = layoutItems[i];
    
    // Check if this item overlaps with any in the current group
    const overlapsWithGroup = currentGroup.some(groupItem => {
      const overlaps = item.startMinutes < groupItem.endMinutes && 
                      item.endMinutes > groupItem.startMinutes;
      if (overlaps) {
        console.log(`Overlap detected: ${item.id} (${item.startMinutes}-${item.endMinutes}) overlaps with ${groupItem.id} (${groupItem.startMinutes}-${groupItem.endMinutes})`);
      }
      return overlaps;
    });

    if (overlapsWithGroup || currentGroup.length === 0) {
      currentGroup.push(item);
      console.log(`Added ${item.id} to current group. Group size: ${currentGroup.length}`);
    } else {
      // Start a new group
      if (currentGroup.length > 0) {
        groups.push([...currentGroup]);
        console.log(`Finished group with ${currentGroup.length} items`);
      }
      currentGroup = [item];
      console.log(`Started new group with ${item.id}`);
    }
  }

  // Add the last group
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  // Layout each group
  groups.forEach(group => {
    if (group.length === 1) {
      // Single appointment - full width
      group[0].column = 0;
      group[0].maxColumns = 1;
      group[0].width = 100;
      group[0].left = 0;
    } else {
      // Multiple overlapping appointments
      layoutOverlappingGroup(group);
    }
  });

  // Sort back to original order
  layoutItems.sort((a, b) => a.originalIndex - b.originalIndex);

  return layoutItems;
}

function layoutOverlappingGroup(group: any[]) {
  // Sort by start time within the group
  group.sort((a, b) => a.startMinutes - b.startMinutes);

  // Assign columns using a greedy algorithm
  const columns: any[][] = [];
  
  group.forEach(item => {
    // Find the first available column
    let assignedColumn = -1;
    
    for (let col = 0; col < columns.length; col++) {
      const lastInColumn = columns[col][columns[col].length - 1];
      if (lastInColumn.endMinutes <= item.startMinutes) {
        // This column is available
        assignedColumn = col;
        break;
      }
    }
    
    if (assignedColumn === -1) {
      // Need a new column
      assignedColumn = columns.length;
      columns.push([]);
    }
    
    columns[assignedColumn].push(item);
    item.column = assignedColumn;
  });

  const totalColumns = columns.length;
  
  // Calculate width and position for each item
  group.forEach(item => {
    item.maxColumns = totalColumns;
    
    // For stacking preference: later items on top
    // Adjust width to create stacking effect
    if (totalColumns > 1) {
      const stackOffset = item.column * 8; // 8px offset per column
      item.width = Math.max(60, 100 - (totalColumns - 1) * 8); // Reduce width slightly
      item.left = stackOffset;
      item.zIndex = 10 + item.column; // Higher z-index for later items
    } else {
      item.width = 100;
      item.left = 0;
      item.zIndex = 10;
    }
  });
}

function timeToMinutes(timeStr: string): number {
  if (!timeStr) return 8 * 60; // Default to 8:00 AM if no time provided
  
  // Handle midnight case from Sikka API
  if (timeStr === "00:00" || timeStr === "0:00") {
    return 8 * 60; // Convert midnight to 8:00 AM
  }
  
  const cleanTime = timeStr.replace(/[^\d:APMapm\s]/g, '').trim();
  const match = cleanTime.match(/(\d{1,2}):?(\d{0,2})\s*(AM|PM|am|pm)?/i);
  
  if (!match) {
    console.warn('Failed to parse time in appointmentLayout:', timeStr, 'defaulting to 8:00 AM');
    return 8 * 60; // Default to 8:00 AM if parsing fails
  }
  
  let hours = parseInt(match[1], 10);
  const minutes = parseInt(match[2] || '0', 10);
  const period = match[3] ? match[3].toUpperCase() : '';
  
  if (period === 'PM' && hours !== 12) {
    hours += 12;
  } else if (period === 'AM' && hours === 12) {
    hours = 0;
  }
  
  const totalMinutes = hours * 60 + minutes;
  
  // If we get midnight (0 minutes), convert to 8:00 AM
  if (totalMinutes === 0) {
    return 8 * 60;
  }
  
  return totalMinutes;
}

// Helper function to determine if an item is an appointment (visual block) or a schedule note (text-only)
function isAppointment(item: any): boolean {
  if (!item || typeof item !== 'object') return false;
  
  // An item is considered an appointment if it has:
  // 1. A patient name (not empty or generic)
  // 2. A specific appointment type/description
  // 3. A reasonable time duration
  const hasPatient = item.patient_name && 
                    item.patient_name !== "No Patient" && 
                    item.patient_name.trim() !== "" &&
                    !item.patient_name.toLowerCase().includes("note") &&
                    !item.patient_name.toLowerCase().includes("block") &&
                    !item.patient_name.toLowerCase().includes("break");
  
  const hasAppointmentType = item.type && 
                            item.type.trim() !== "" &&
                            !item.type.toLowerCase().includes("note") &&
                            !item.type.toLowerCase().includes("break") &&
                            !item.type.toLowerCase().includes("block");
  
  const hasReasonableDuration = item.length && Number(item.length) >= 15; // At least 15 minutes
  
  // If it's explicitly marked as blocked or a note, treat as schedule note
  if (item.isBlocked || 
      (item.type && item.type.toLowerCase().includes("note")) ||
      (item.description && item.description.toLowerCase().includes("note")) ||
      (item.patient_name && item.patient_name.toLowerCase().includes("note"))) {
    return false;
  }
  
  return hasPatient && (hasAppointmentType || hasReasonableDuration);
}

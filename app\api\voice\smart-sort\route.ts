import { NextRequest, NextResponse } from 'next/server';
import { AzurePostgresDB } from '@/lib/azure-postgres-db';
import { featureFlags } from '@/lib/feature-flags';

interface TranscriptionFile {
  transcriptionId: string;
  filename: string;
  transcriptionText: string;
  createdAt: string;
  confidence: number;
  deviceId: string;
  patientId?: string;
}

interface AppointmentMatch {
  appointmentId: string;
  patientName: string;
  provider: string;
  operatory: string;
  startTime: string;
  endTime: string;
  appointmentType: string;
  description: string;
  confidence: number;
  matchReason: string;
}

interface SmartSortSuggestion {
  transcription: TranscriptionFile;
  suggestedMatches: AppointmentMatch[];
  autoMatchConfidence: number;
  recommendedMatch?: AppointmentMatch;
}

interface SmartSortResult {
  date: string;
  totalTranscriptions: number;
  sortableTranscriptions: number;
  suggestions: SmartSortSuggestion[];
  autoMatches: number;
  manualReviewNeeded: number;
  availableAppointments: AppointmentMatch[];
}

/**
 * SMART SORT ENDPOINT - Transcription-Only Architecture
 * Intelligently matches transcriptions to appointments using AI analysis
 * Uses Vercel Postgres for transcription data and content-based matching
 */
export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.useAzurePostgres) {
      return NextResponse.json({
        error: 'Azure Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { transcriptionIds, date, matchingOptions = {} } = await request.json();

    if (!transcriptionIds && !date) {
      return NextResponse.json({
        error: 'Either transcription IDs or date is required',
        code: 'MISSING_PARAMETERS'
      }, { status: 400 });
    }

    console.log(`🧠 Smart sorting transcriptions for date: ${date || 'specific IDs'}`);

    let transcriptions: TranscriptionFile[] = [];

    if (transcriptionIds && Array.isArray(transcriptionIds)) {
      // Get specific transcriptions
      const allTranscriptions = [];
      
      for (const id of transcriptionIds) {
        const transcription = await AzurePostgresDB.getTranscription(id);
        if (transcription && transcription.transcription_text && transcription.transcription_text.trim()) {
          allTranscriptions.push({
            transcriptionId: transcription.id.toString(),
            filename: transcription.filename,
            transcriptionText: transcription.transcription_text,
            createdAt: transcription.created_at,
            confidence: transcription.confidence_score || 0,
            deviceId: (transcription.metadata as any)?.device_id || '',
            patientId: transcription.patient_id || undefined
          });
        }
      }
      
      transcriptions = allTranscriptions;
    } else if (date) {
      // Get transcriptions for specific date
      const allTranscriptions = await AzurePostgresDB.getTranscriptions({ limit: 50 });
      
      transcriptions = allTranscriptions
        .filter(t => {
          const transcriptionDate = t.created_at.split('T')[0];
          return transcriptionDate === date && t.transcription_text && t.transcription_text.trim();
        })
        .map(t => ({
          transcriptionId: t.id.toString(),
          filename: t.filename,
          transcriptionText: t.transcription_text!,
          createdAt: t.created_at,
          confidence: t.confidence_score || 0,
          deviceId: (t.metadata as any)?.device_id || '',
          patientId: t.patient_id || undefined
        }));
    }

    if (transcriptions.length === 0) {
      return NextResponse.json({
        date: date || 'specific',
        totalTranscriptions: 0,
        sortableTranscriptions: 0,
        suggestions: [],
        autoMatches: 0,
        manualReviewNeeded: 0,
        availableAppointments: [],
        message: 'No transcriptions found for sorting'
      });
    }

    // Get appointments for the date (if provided) or extract dates from transcriptions
    let availableAppointments: AppointmentMatch[] = [];

    if (date) {
      availableAppointments = await getAppointmentsForDate(date);
    } else {
      // Extract unique dates from transcriptions and get appointments
      const uniqueDates = [...new Set(transcriptions.map(t =>
        t.createdAt.split('T')[0]
      ))];

      for (const dateStr of uniqueDates) {
        const dayAppointments = await getAppointmentsForDate(dateStr);
        availableAppointments.push(...dayAppointments);
      }
    }

    console.log(`📅 Found ${availableAppointments.length} appointments to match against`);

    const suggestions: SmartSortSuggestion[] = [];
    let autoMatches = 0;
    let manualReviewNeeded = 0;

    // Process each transcription for smart matching
    for (const transcription of transcriptions) {
      const suggestion = await generateSmartSortSuggestion(
        transcription,
        availableAppointments,
        matchingOptions
      );

      suggestions.push(suggestion);

      // Determine if auto-match is recommended
      const autoMatchThreshold = matchingOptions.autoMatchThreshold || 0.8;
      if (suggestion.autoMatchConfidence >= autoMatchThreshold && suggestion.recommendedMatch) {
        autoMatches++;
      } else {
        manualReviewNeeded++;
      }
    }

    const result: SmartSortResult = {
      date: date || 'multiple',
      totalTranscriptions: transcriptions.length,
      sortableTranscriptions: suggestions.length,
      suggestions,
      autoMatches,
      manualReviewNeeded,
      availableAppointments
    };

    console.log(`✅ Smart sort complete: ${autoMatches} auto-matches, ${manualReviewNeeded} need review`);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Smart sort error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Smart sort failed',
      code: 'SMART_SORT_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to retrieve appointments for a specific date
 */
export async function GET(request: NextRequest) {
  try {
    if (!featureFlags.useAzurePostgres) {
      return NextResponse.json({
        error: 'Azure Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json({
        error: 'Date parameter is required',
        code: 'MISSING_DATE'
      }, { status: 400 });
    }

    const appointments = await getAppointmentsForDate(date);

    return NextResponse.json({
      success: true,
      date,
      appointments,
      count: appointments.length
    });

  } catch (error) {
    console.error('❌ Error getting appointments for smart sort:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to get appointments',
      code: 'APPOINTMENTS_ERROR'
    }, { status: 500 });
  }
}

async function getAppointmentsForDate(date: string): Promise<AppointmentMatch[]> {
  try {
    // Get appointments using AzurePostgresDB
    const appointments = await AzurePostgresDB.getAppointmentsByDate(date);
    
    return appointments.map(appt => ({
      appointmentId: appt.sikka_id,
      patientName: appt.patient_name,
      provider: appt.provider,
      operatory: appt.operatory,
      startTime: appt.appointment_time,
      endTime: appt.appointment_time, // No end time in the schema, using start time
      appointmentType: appt.appointment_type || '',
      description: appt.notes || '',
      confidence: 0,
      matchReason: ''
    }));
  } catch (error) {
    console.warn(`No appointments found for ${date} - appointments table may not exist:`, error);
    return [];
  }
}

async function generateSmartSortSuggestion(
  transcription: TranscriptionFile,
  appointments: AppointmentMatch[],
  options: any = {}
): Promise<SmartSortSuggestion> {

  const suggestedMatches: AppointmentMatch[] = [];

  console.log(`🔍 Analyzing transcription ${transcription.filename} (${transcription.transcriptionText.length} chars)`);

  if (appointments.length === 0) {
    return {
      transcription,
      suggestedMatches: [],
      autoMatchConfidence: 0,
      recommendedMatch: undefined
    };
  }

  // Extract time from filename if possible
  const timeFromFilename = extractTimeFromFilename(transcription.filename);
  const transcriptionDate = transcription.createdAt.split('T')[0];

  // Score each appointment based on various factors
  for (const appointment of appointments) {
    let confidence = 0;
    const matchReasons: string[] = [];

    // 1. Content-based matching (primary signal)
    const contentScore = calculateContentMatchScore(
      transcription.transcriptionText,
      appointment
    );
    confidence += contentScore * 0.5; // 50% weight

    if (contentScore > 0.7) {
      matchReasons.push('Strong content match with appointment details');
    } else if (contentScore > 0.4) {
      matchReasons.push('Moderate content correlation');
    }

    // 2. Time-based matching
    if (timeFromFilename) {
      const timeScore = calculateTimeMatchScore(
        timeFromFilename,
        appointment.startTime,
        appointment.endTime
      );
      confidence += timeScore * 0.3; // 30% weight

      if (timeScore > 0.8) {
        matchReasons.push(`Recording time (${timeFromFilename}) matches appointment time`);
      } else if (timeScore > 0.5) {
        matchReasons.push(`Recording time near appointment time`);
      }
    }

    // 3. Patient name matching (if available)
    if (transcription.patientId) {
      // If transcription already has patient ID, perfect match
      if (transcription.patientId === appointment.appointmentId) {
        confidence += 0.15; // 15% weight
        matchReasons.push('Patient ID matches appointment');
      }
    } else {
      // Content-based patient name extraction
      const nameScore = calculatePatientNameMatch(
        transcription.transcriptionText,
        appointment.patientName
      );
      confidence += nameScore * 0.15; // 15% weight

      if (nameScore > 0.7) {
        matchReasons.push(`Patient name "${appointment.patientName}" mentioned in transcription`);
      }
    }

    // 4. Provider/operatory matching
    const locationScore = calculateLocationMatch(
      transcription.transcriptionText,
      appointment.provider,
      appointment.operatory
    );
    confidence += locationScore * 0.05; // 5% weight

    if (locationScore > 0.5) {
      matchReasons.push('Provider or operatory mentioned in transcription');
    }

    // Only include matches with reasonable confidence
    if (confidence > 0.2) {
      suggestedMatches.push({
        ...appointment,
        confidence: Math.min(confidence, 1.0),
        matchReason: matchReasons.join('; ')
      });
    }
  }

  // Sort by confidence (highest first)
  suggestedMatches.sort((a, b) => b.confidence - a.confidence);

  // Determine auto-match confidence and recommended match
  let autoMatchConfidence = 0;
  let recommendedMatch: AppointmentMatch | undefined;

  if (suggestedMatches.length > 0) {
    const topMatch = suggestedMatches[0];
    autoMatchConfidence = topMatch.confidence;

    console.log(`🎯 Match analysis for ${transcription.filename}:`);
    console.log(`   Top match confidence: ${autoMatchConfidence.toFixed(3)} - ${topMatch.matchReason}`);
    console.log(`   Appointment: ${topMatch.patientName} at ${topMatch.startTime} with ${topMatch.provider}`);

    // Recommend auto-match if confidence is high and significantly better than second option
    const confidenceThreshold = options.autoMatchThreshold || 0.8;
    if (autoMatchConfidence >= confidenceThreshold) {
      const secondBestConfidence = suggestedMatches.length > 1 ? suggestedMatches[1].confidence : 0;
      const confidenceGap = options.confidenceGap || 0.15;

      if (autoMatchConfidence - secondBestConfidence >= confidenceGap) {
        recommendedMatch = topMatch;
        console.log(`✅ Auto-match recommended: confidence ${autoMatchConfidence.toFixed(3)}`);
      } else {
        console.log(`⚠️ Manual review needed: confidence gap too small`);
      }
    } else {
      console.log(`❌ Manual review needed: confidence ${autoMatchConfidence.toFixed(3)} below threshold`);
    }
  }

  return {
    transcription,
    suggestedMatches: suggestedMatches.slice(0, 5), // Top 5 suggestions
    autoMatchConfidence,
    recommendedMatch
  };
}

function extractTimeFromFilename(filename: string): string | null {
  // Common voice recorder filename patterns
  const patterns = [
    /(\d{2})(\d{2})(\d{2})_(\d{2})(\d{2})/,  // YYMMDD_HHMM
    /(\d{6})_(\d{4})/,                        // YYMMDD_HHMM
    /_(\d{2})(\d{2})\.mp3$/,                  // _HHMM.mp3
    /_(\d{4})\.mp3$/                          // _HHMM.mp3
  ];

  for (const pattern of patterns) {
    const match = filename.match(pattern);
    if (match) {
      // Extract time components based on pattern
      if (pattern.source.includes('_')) {
        const timeStr = match[match.length - 1] || match[match.length - 2];
        if (timeStr && timeStr.length >= 4) {
          const hours = timeStr.substring(0, 2);
          const minutes = timeStr.substring(2, 4);
          return `${hours}:${minutes}`;
        }
      }
    }
  }

  return null;
}

function calculateContentMatchScore(transcriptionText: string, appointment: AppointmentMatch): number {
  const content = transcriptionText.toLowerCase();
  const patientName = appointment.patientName.toLowerCase();
  const provider = appointment.provider.toLowerCase();
  const type = appointment.appointmentType.toLowerCase();
  const description = appointment.description.toLowerCase();

  let score = 0;
  let matches = 0;

  // Patient name in content
  if (patientName && content.includes(patientName)) {
    score += 0.4;
    matches++;
  }

  // Provider name in content
  if (provider && content.includes(provider)) {
    score += 0.2;
    matches++;
  }

  // Appointment type keywords
  const typeKeywords = type.split(' ');
  for (const keyword of typeKeywords) {
    if (keyword.length > 3 && content.includes(keyword)) {
      score += 0.1;
      matches++;
    }
  }

  // Description keywords
  const descKeywords = description.split(' ');
  for (const keyword of descKeywords) {
    if (keyword.length > 4 && content.includes(keyword)) {
      score += 0.05;
      matches++;
    }
  }

  // Dental procedure keywords matching
  const dentalKeywords = [
    'cleaning', 'examination', 'exam', 'checkup', 'cavity', 'filling',
    'crown', 'root canal', 'extraction', 'x-ray', 'pain', 'tooth'
  ];

  for (const keyword of dentalKeywords) {
    if (content.includes(keyword) && (type.includes(keyword) || description.includes(keyword))) {
      score += 0.1;
      matches++;
    }
  }

  return Math.min(score, 1.0);
}

function calculateTimeMatchScore(recordingTime: string, appointmentStart: string, appointmentEnd: string): number {
  try {
    const recordingMinutes = timeToMinutes(recordingTime);
    const startMinutes = timeToMinutes(appointmentStart);
    const endMinutes = timeToMinutes(appointmentEnd);

    // Perfect match if recording time is within appointment window
    if (recordingMinutes >= startMinutes && recordingMinutes <= endMinutes) {
      return 1.0;
    }

    // Calculate distance from appointment window
    const distanceFromStart = Math.abs(recordingMinutes - startMinutes);
    const distanceFromEnd = Math.abs(recordingMinutes - endMinutes);
    const minDistance = Math.min(distanceFromStart, distanceFromEnd);

    // Score decreases with distance (30 minutes = 0 score)
    return Math.max(0, 1 - (minDistance / 30));

  } catch (error) {
    return 0;
  }
}

function calculatePatientNameMatch(transcriptionText: string, patientName: string): number {
  const content = transcriptionText.toLowerCase();
  const name = patientName.toLowerCase();

  // Split name into parts
  const nameParts = name.split(' ').filter(part => part.length > 1);
  let matchCount = 0;

  for (const part of nameParts) {
    if (content.includes(part)) {
      matchCount++;
    }
  }

  return nameParts.length > 0 ? matchCount / nameParts.length : 0;
}

function calculateLocationMatch(transcriptionText: string, provider: string, operatory: string): number {
  const content = transcriptionText.toLowerCase();
  let score = 0;

  if (provider && content.includes(provider.toLowerCase())) {
    score += 0.6;
  }

  if (operatory && content.includes(operatory.toLowerCase())) {
    score += 0.4;
  }

  return Math.min(score, 1.0);
}

function timeToMinutes(timeStr: string): number {
  if (!timeStr) return 8 * 60; // Default to 8:00 AM if no time provided
  
  // Handle midnight case from Sikka API
  if (timeStr === "00:00" || timeStr === "0:00") {
    return 8 * 60; // Convert midnight to 8:00 AM
  }

  try {
    // Handle AM/PM format
    const ampmMatch = timeStr.match(/(\d{1,2}):?(\d{0,2})\s*(AM|PM|am|pm)/i);
    if (ampmMatch) {
      let hours = parseInt(ampmMatch[1], 10);
      const minutes = parseInt(ampmMatch[2] || '0', 10);
      const period = ampmMatch[3].toUpperCase();
      
      if (period === 'PM' && hours !== 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }
      
      return hours * 60 + minutes;
    }

    // Handle 24-hour format
    const [hours, minutes] = timeStr.split(':').map(Number);
    const totalMinutes = hours * 60 + (minutes || 0);
    
    // If we get midnight (0 minutes), convert to 8:00 AM
    if (totalMinutes === 0) {
      return 8 * 60;
    }
    
    return totalMinutes;
  } catch (error) {
    console.warn('Failed to parse time in smart-sort:', timeStr, 'defaulting to 8:00 AM');
    return 8 * 60;
  }
}
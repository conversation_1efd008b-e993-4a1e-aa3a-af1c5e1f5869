import { SikkaApiClient } from './api/sikka-client';
import { loadCredentials } from './api/credentials';

export interface VoiceWorkflowRecording {
  id: string;
  filename: string;
  recording_date: string;
  transcription?: string;
  transcription_confidence?: number;
  assigned_patient_id?: string;
  assigned_appointment_id?: string;
  assigned_appointment_date?: string;
  status: 'unassigned' | 'assigned' | 'transcribing' | 'transcribed' | 'reviewing' | 'completed';
}

export interface WorkflowAppointment {
  sikka_id: string;
  patient_id: string;
  patient_name: string;
  appointment_date: string;
  appointment_time: string;
  operatory: string;
  provider: string;
  appointment_type?: string;
  chart_notes?: string;
  chart_notes_fetched_at?: string;
  recording_status: 'has_recording' | 'no_recording';
  workflow_status: 'pending' | 'in_progress' | 'completed';
  professionalized_note?: string;
  professionalized_at?: string;
}

export interface DailyWorkflowSummary {
  date: string;
  total_appointments: number;
  appointments_with_recordings: number;
  appointments_without_recordings: number;
  completed_notes: number;
  pending_notes: number;
  unmatched_recordings: number;
}

export interface WorkingMemoryEntry {
  appointment_id: string;
  patient_id: string;
  patient_name: string;
  appointment_date: string;
  appointment_time: string;
  operatory: string;
  provider: string;
  
  // Voice data
  recording_id?: string;
  transcription?: string;
  transcription_confidence?: number;
  
  // Chart notes from Sikka
  chart_notes?: string;
  chart_notes_fetched_at?: string;
  
  // Generated content
  professionalized_note?: string;
  professionalized_at?: string;
  
  // Status tracking
  workflow_status: 'pending' | 'processing' | 'completed' | 'error';
  last_updated: string;
}

export class VoiceWorkflowService {
  private sikkaClient: SikkaApiClient;
  private workingMemory: Map<string, WorkingMemoryEntry> = new Map();

  constructor() {
    this.sikkaClient = new SikkaApiClient(loadCredentials());
  }

  /**
   * Main daily workflow process
   * 1. Fetch all appointments for the day
   * 2. Fetch all recordings for the day
   * 3. Match recordings to appointments
   * 4. Fetch chart notes for all appointments
   * 5. Store everything in working memory
   */
  async processDailyWorkflow(date: string): Promise<{
    summary: DailyWorkflowSummary;
    appointments: WorkflowAppointment[];
    unmatched_recordings: VoiceWorkflowRecording[];
  }> {
    console.log(`🔄 Starting daily voice workflow for ${date}`);

    try {
      // Step 1: Authenticate with Sikka
      await this.sikkaClient.authenticate();

      // Step 2: Fetch appointments for the day
      const appointments = await this.fetchDayAppointments(date);
      console.log(`📅 Found ${appointments.length} appointments for ${date}`);

      // Step 3: Fetch recordings for the day
      const recordings = await this.fetchDayRecordings(date);
      console.log(`🎤 Found ${recordings.length} recordings for ${date}`);

      // Step 4: Match recordings to appointments
      const { matched, unmatched } = await this.matchRecordingsToAppointments(recordings, appointments);
      console.log(`✅ Matched ${matched.length} recordings, ${unmatched.length} unmatched`);

      // Step 5: Fetch chart notes for all appointments
      await this.fetchChartNotesForAppointments(appointments);

      // Step 6: Store in working memory
      await this.storeInWorkingMemory(appointments, matched);

      // Step 7: Generate summary
      const summary = this.generateDailySummary(date, appointments, unmatched);

      return {
        summary,
        appointments: appointments.map(apt => this.convertToWorkflowAppointment(apt, matched)),
        unmatched_recordings: unmatched
      };

    } catch (error) {
      console.error('❌ Daily workflow error:', error);
      throw error;
    }
  }

  /**
   * Fetch all appointments for a specific date from Sikka
   */
  private async fetchDayAppointments(date: string): Promise<any[]> {
    try {
      const appointments = await this.sikkaClient.getAppointments(date);
      return appointments.filter(apt => 
        apt.patient_name && 
        apt.patient_name !== "No Patient" &&
        apt.patient_name !== "Unknown Patient"
      );
    } catch (error) {
      console.error('Error fetching appointments:', error);
      return [];
    }
  }

  /**
   * Fetch all voice recordings for a specific date
   */
  private async fetchDayRecordings(date: string): Promise<VoiceWorkflowRecording[]> {
    try {
      // This would typically fetch from your voice recordings database
      // For now, we'll use a placeholder implementation
      const response = await fetch(`/api/voice/recordings?date=${date}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch recordings: ${response.status}`);
      }
      const data = await response.json();
      return data.recordings || [];
    } catch (error) {
      console.error('Error fetching recordings:', error);
      return [];
    }
  }

  /**
   * Match recordings to appointments using various strategies
   */
  private async matchRecordingsToAppointments(
    recordings: VoiceWorkflowRecording[], 
    appointments: any[]
  ): Promise<{ matched: any[], unmatched: VoiceWorkflowRecording[] }> {
    const matched: any[] = [];
    const unmatched: VoiceWorkflowRecording[] = [];

    for (const recording of recordings) {
      // Skip already assigned recordings
      if (recording.assigned_appointment_id) {
        matched.push({
          ...recording,
          appointment: appointments.find(apt => apt.id === recording.assigned_appointment_id)
        });
        continue;
      }

      // Try to match based on timing, patient name, etc.
      const matchedAppointment = this.findBestAppointmentMatch(recording, appointments);
      
      if (matchedAppointment) {
        matched.push({
          ...recording,
          appointment: matchedAppointment,
          assigned_appointment_id: matchedAppointment.id,
          assigned_patient_id: matchedAppointment.patient_id
        });
      } else {
        unmatched.push(recording);
      }
    }

    return { matched, unmatched };
  }

  /**
   * Find the best appointment match for a recording
   */
  private findBestAppointmentMatch(recording: VoiceWorkflowRecording, appointments: any[]): any | null {
    // Simple matching logic - can be enhanced with ML/AI
    const recordingDate = new Date(recording.recording_date);
    
    // Look for appointments on the same day
    const sameDayAppointments = appointments.filter(apt => {
      const aptDate = new Date(apt.appointment_date);
      return aptDate.toDateString() === recordingDate.toDateString();
    });

    // For now, return the first same-day appointment
    // In a real implementation, you'd use more sophisticated matching
    return sameDayAppointments[0] || null;
  }

  /**
   * Fetch chart notes for all appointments from Sikka
   */
  private async fetchChartNotesForAppointments(appointments: any[]): Promise<void> {
    console.log(`📋 Fetching chart notes for ${appointments.length} appointments`);

    for (const appointment of appointments) {
      if (!appointment.patient_id) continue;

      try {
        // Fetch clinical notes for this patient on this date
        const notes = await this.sikkaClient.getClinicalNotes(
          appointment.patient_id, 
          appointment.appointment_date
        );

        appointment.chart_notes = notes.map((note: any) => note.note_text).join('\n\n');
        appointment.chart_notes_fetched_at = new Date().toISOString();

      } catch (error) {
        console.error(`Error fetching chart notes for patient ${appointment.patient_id}:`, error);
        appointment.chart_notes = null;
      }
    }
  }

  /**
   * Store appointments and recordings in working memory
   */
  private async storeInWorkingMemory(appointments: any[], matchedRecordings: any[]): Promise<void> {
    console.log(`💾 Storing ${appointments.length} appointments in working memory`);

    for (const appointment of appointments) {
      const matchedRecording = matchedRecordings.find(rec => 
        rec.assigned_appointment_id === appointment.id
      );

      const entry: WorkingMemoryEntry = {
        appointment_id: appointment.id,
        patient_id: appointment.patient_id,
        patient_name: appointment.patient_name,
        appointment_date: appointment.appointment_date,
        appointment_time: appointment.appointment_time,
        operatory: appointment.operatory,
        provider: appointment.provider,
        
        // Voice data
        recording_id: matchedRecording?.id,
        transcription: matchedRecording?.transcription,
        transcription_confidence: matchedRecording?.transcription_confidence,
        
        // Chart notes
        chart_notes: appointment.chart_notes,
        chart_notes_fetched_at: appointment.chart_notes_fetched_at,
        
        // Status
        workflow_status: 'pending',
        last_updated: new Date().toISOString()
      };

      this.workingMemory.set(appointment.id, entry);
    }
  }

  /**
   * Generate professionalized note combining transcription and chart notes
   */
  async generateProfessionalizedNote(appointmentId: string): Promise<string> {
    const entry = this.workingMemory.get(appointmentId);
    if (!entry) {
      throw new Error(`Appointment ${appointmentId} not found in working memory`);
    }

    entry.workflow_status = 'processing';
    entry.last_updated = new Date().toISOString();

    try {
      const response = await fetch('/api/voice-workflow/generate-note', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          patient_name: entry.patient_name,
          appointment_date: entry.appointment_date,
          appointment_time: entry.appointment_time,
          operatory: entry.operatory,
          provider: entry.provider,
          transcription: entry.transcription,
          chart_notes: entry.chart_notes
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate note: ${response.status}`);
      }

      const data = await response.json();
      const professionalizedNote = data.professionalized_note;

      // Update working memory
      entry.professionalized_note = professionalizedNote;
      entry.professionalized_at = new Date().toISOString();
      entry.workflow_status = 'completed';
      entry.last_updated = new Date().toISOString();

      return professionalizedNote;

    } catch (error) {
      entry.workflow_status = 'error';
      entry.last_updated = new Date().toISOString();
      throw error;
    }
  }

  /**
   * Get working memory entry for an appointment
   */
  getWorkingMemoryEntry(appointmentId: string): WorkingMemoryEntry | null {
    return this.workingMemory.get(appointmentId) || null;
  }

  /**
   * Get all working memory entries
   */
  getAllWorkingMemoryEntries(): WorkingMemoryEntry[] {
    return Array.from(this.workingMemory.values());
  }

  /**
   * Convert appointment to workflow appointment format
   */
  private convertToWorkflowAppointment(appointment: any, matchedRecordings: any[]): WorkflowAppointment {
    const hasRecording = matchedRecordings.some(rec => 
      rec.assigned_appointment_id === appointment.id
    );

    return {
      sikka_id: appointment.id,
      patient_id: appointment.patient_id,
      patient_name: appointment.patient_name,
      appointment_date: appointment.appointment_date,
      appointment_time: appointment.appointment_time,
      operatory: appointment.operatory,
      provider: appointment.provider,
      appointment_type: appointment.appointment_type,
      chart_notes: appointment.chart_notes,
      chart_notes_fetched_at: appointment.chart_notes_fetched_at,
      recording_status: hasRecording ? 'has_recording' : 'no_recording',
      workflow_status: 'pending'
    };
  }

  /**
   * Generate daily summary statistics
   */
  private generateDailySummary(
    date: string, 
    appointments: any[], 
    unmatchedRecordings: VoiceWorkflowRecording[]
  ): DailyWorkflowSummary {
    const appointmentsWithRecordings = appointments.filter(apt => 
      this.workingMemory.get(apt.id)?.recording_id
    ).length;

    const completedNotes = Array.from(this.workingMemory.values()).filter(entry => 
      entry.professionalized_note
    ).length;

    return {
      date,
      total_appointments: appointments.length,
      appointments_with_recordings: appointmentsWithRecordings,
      appointments_without_recordings: appointments.length - appointmentsWithRecordings,
      completed_notes: completedNotes,
      pending_notes: appointments.length - completedNotes,
      unmatched_recordings: unmatchedRecordings.length
    };
  }
}

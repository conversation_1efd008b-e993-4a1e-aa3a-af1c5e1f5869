"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { format } from "date-fns";
import { useTheme } from "next-themes";
import { PageHeader } from "@/components/ui/page-header";
import { DailyVoiceWorkflow } from "@/components/voice-workflow/daily-voice-workflow";
import { 
  Calendar, 
  Mic, 
  FileText, 
  ArrowLeft, 
  ArrowRight,
  RefreshCw,
  Settings,
  BarChart3
} from "lucide-react";

interface DailyWorkflowSummary {
  date: string;
  total_appointments: number;
  appointments_with_recordings: number;
  appointments_without_recordings: number;
  completed_notes: number;
  pending_notes: number;
  unmatched_recordings: number;
}

function VoiceWorkflowScheduleContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { theme } = useTheme();
  const [workflowSummary, setWorkflowSummary] = useState<DailyWorkflowSummary | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get date from URL or use current date
  const dateParam = searchParams?.get("date") || "";
  let formattedDate: string;
  
  if (dateParam && dateParam.match(/^\d{4}-\d{2}-\d{2}$/)) {
    formattedDate = dateParam;
  } else {
    const now = new Date();
    formattedDate = format(now, 'yyyy-MM-dd');
  }

  const [year, month, day] = formattedDate.split('-').map(Number);
  const parsedDate = new Date(year, month - 1, day, 12, 0, 0);

  // Navigation functions
  const goToPreviousDay = () => {
    const currentDate = new Date(year, month - 1, day);
    currentDate.setDate(currentDate.getDate() - 1);
    const newDate = format(currentDate, 'yyyy-MM-dd');
    router.push(`/voice-workflow-schedule?date=${newDate}`);
  };

  const goToNextDay = () => {
    const currentDate = new Date(year, month - 1, day);
    currentDate.setDate(currentDate.getDate() + 1);
    const newDate = format(currentDate, 'yyyy-MM-dd');
    router.push(`/voice-workflow-schedule?date=${newDate}`);
  };

  const goToToday = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    router.push(`/voice-workflow-schedule?date=${today}`);
  };

  const handleWorkflowUpdate = (summary: DailyWorkflowSummary) => {
    setWorkflowSummary(summary);
  };

  const handleBackClick = () => {
    router.push('/');
  };

  // Calculate completion percentage
  const completionPercentage = workflowSummary 
    ? Math.round((workflowSummary.completed_notes / Math.max(workflowSummary.total_appointments, 1)) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <PageHeader
        title="Voice Workflow Schedule"
        showBackButton
        onBackClick={handleBackClick}
        backButtonLabel="Back to Home"
        activeTab="voice-workflow"
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          {/* Mobile: Stack title and controls vertically */}
          <div className="sm:hidden space-y-4">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Voice Workflow
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
                {parsedDate.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  timeZone: 'UTC'
                })}
              </p>
            </div>

            {/* Mobile Navigation */}
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={goToPreviousDay}
                className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                title="Previous Day"
              >
                <ArrowLeft className="h-4 w-4" />
              </button>
              
              <button
                onClick={goToToday}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors"
              >
                Today
              </button>
              
              <button
                onClick={goToNextDay}
                className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                title="Next Day"
              >
                <ArrowRight className="h-4 w-4" />
              </button>
            </div>

            {/* Mobile Summary Cards */}
            {workflowSummary && (
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Appointments</span>
                  </div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white mt-1">
                    {workflowSummary.total_appointments}
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <Mic className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">With Recordings</span>
                  </div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white mt-1">
                    {workflowSummary.appointments_with_recordings}
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-purple-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                  </div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white mt-1">
                    {workflowSummary.completed_notes}
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4 text-orange-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Progress</span>
                  </div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white mt-1">
                    {completionPercentage}%
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Desktop: Side by side layout */}
          <div className="hidden sm:block">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Voice Workflow Schedule
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
                  {parsedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    timeZone: 'UTC'
                  })}
                </p>
              </div>

              {/* Desktop Navigation */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={goToPreviousDay}
                  className="flex items-center space-x-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                  title="Previous Day"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Previous</span>
                </button>
                
                <button
                  onClick={goToToday}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition-colors"
                >
                  Today
                </button>
                
                <button
                  onClick={goToNextDay}
                  className="flex items-center space-x-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                  title="Next Day"
                >
                  <span>Next</span>
                  <ArrowRight className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Desktop Summary Bar */}
            {workflowSummary && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
                <div className="grid grid-cols-5 gap-6">
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Calendar className="h-5 w-5 text-blue-600" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Appointments</span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {workflowSummary.total_appointments}
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Mic className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">With Recordings</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">
                      {workflowSummary.appointments_with_recordings}
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <FileText className="h-5 w-5 text-purple-600" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed Notes</span>
                    </div>
                    <div className="text-2xl font-bold text-purple-600">
                      {workflowSummary.completed_notes}
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <RefreshCw className="h-5 w-5 text-orange-600" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Unmatched</span>
                    </div>
                    <div className="text-2xl font-bold text-orange-600">
                      {workflowSummary.unmatched_recordings}
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <BarChart3 className="h-5 w-5 text-indigo-600" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</span>
                    </div>
                    <div className="text-2xl font-bold text-indigo-600">
                      {completionPercentage}%
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                    <span>Workflow Completion</span>
                    <span>{completionPercentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${completionPercentage}%` }}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <Mic className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                Voice Workflow Instructions
              </h3>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Drag and drop</strong> voice recordings from the left panel onto appointment cards to match them. 
                Once matched, you can generate professionalized notes that combine the voice transcription with existing chart notes.
              </p>
            </div>
          </div>
        </div>

        {/* Main Workflow Component */}
        <DailyVoiceWorkflow
          date={formattedDate}
          isDarkMode={theme === 'dark'}
          onWorkflowUpdate={handleWorkflowUpdate}
        />
      </main>
    </div>
  );
}

export default function VoiceWorkflowSchedulePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading voice workflow...</p>
        </div>
      </div>
    }>
      <VoiceWorkflowScheduleContent />
    </Suspense>
  );
}

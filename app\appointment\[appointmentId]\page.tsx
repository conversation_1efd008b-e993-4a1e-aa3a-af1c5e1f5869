'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { PageHeader } from '../../../src/components/ui/page-header';
import { ClinicalNotesDisplay } from '../../../src/components/clinical-notes/clinical-notes-display';
import { VoiceRecordingsForAppointment } from '../../../src/components/voice/voice-recordings-for-appointment';
import { formatPatientName } from '../../../src/lib/utils/patient-name-formatter';

interface Appointment {
  id: string;
  patient_name: string;
  patient_id?: string;
  startTime: string;
  endTime?: string;
  length?: number;
  type?: string;
  operatory?: string;
  provider?: string | { href: string } | any;
  status?: string;
  notes?: string;
  patient_dob?: string;
  patient_phone?: string;
  patient_email?: string;
}

export default function AppointmentPage() {
  const params = useParams();
  const router = useRouter();
  const { theme } = useTheme();
  const appointmentId = params?.appointmentId as string;

  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!appointmentId) return;

    const fetchAppointment = async () => {
      try {
        setLoading(true);
        console.log(`🔍 Fetching appointment details for ID: ${appointmentId}`);

        const response = await fetch(`/api/appointments-sql/${appointmentId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'same-origin' // Include authentication cookies
        });

        console.log(`📡 API Response status: ${response.status}`);
        console.log(`📡 API Response URL: ${response.url}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ API Error Response: ${errorText}`);
          throw new Error(`Failed to fetch appointment: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ Appointment data received:', data);
        setAppointment(data);
      } catch (err) {
        console.error('❌ Error fetching appointment:', err);
        setError(err instanceof Error ? err.message : 'Failed to load appointment');
      } finally {
        setLoading(false);
      }
    };

    fetchAppointment();
  }, [appointmentId]);

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    try {
      const [hours, minutes] = timeString.split(':');
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? 'PM' : 'AM';
      const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
      return `${displayHour}:${minutes} ${ampm}`;
    } catch {
      return timeString;
    }
  };

  const formatDuration = (minutes: number) => {
    if (!minutes) return '';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Get appointment date from appointment data, URL params, or use today as fallback
  const getAppointmentDate = (): string => {
    try {
      // First priority: Use the actual appointment date from the appointment data
      console.log('🗓️ getAppointmentDate - appointment.startTime:', appointment?.startTime);
      console.log('🗓️ getAppointmentDate - full appointment object:', appointment);
      if (appointment?.startTime) {
        // Ensure the date is in YYYY-MM-DD format
        const appointmentDate = new Date(appointment.startTime);
        if (!isNaN(appointmentDate.getTime())) {
          const formattedDate = appointmentDate.toISOString().split('T')[0];
          console.log('🗓️ Using appointment date:', formattedDate);
          return formattedDate;
        }
      }

      // Second priority: Try to get date from URL search params (if coming from schedule)
      const urlParams = new URLSearchParams(window.location.search);
      const dateParam = urlParams.get('date');
      if (dateParam) {
        return dateParam;
      }

      // Third priority: Try to get date from referrer URL
      if (document.referrer) {
        const referrerUrl = new URL(document.referrer);
        const referrerDate = referrerUrl.searchParams.get('date');
        if (referrerDate) {
          return referrerDate;
        }
      }

      // Fallback to today's date
      console.warn('No appointment date found, using today as fallback');
      return new Date().toISOString().split('T')[0];
    } catch (error) {
      console.warn('Could not determine appointment date, using today:', error);
      return new Date().toISOString().split('T')[0];
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <PageHeader
          title="Dentalapp"
          showBackButton
          onBackClick={() => router.back()}
          backButtonLabel="Back"
        />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error || !appointment) {
    const appointmentDate = getAppointmentDate();

    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <PageHeader
          title="Dentalapp"
          showBackButton
          onBackClick={() => router.back()}
          backButtonLabel="Back"
        />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {error ? 'Error Loading Appointment' : 'Appointment Not Found'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {error || 'The requested appointment could not be found. It may have been moved, cancelled, or the ID may be invalid.'}
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={() => router.push(`/schedule?date=${appointmentDate}`)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                >
                  View Schedule for {new Date(appointmentDate).toLocaleDateString()}
                </button>
                <button
                  onClick={() => router.back()}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <PageHeader
        title="Dentalapp"
        showBackButton
        onBackClick={() => router.back()}
        backButtonLabel="Back to Schedule"
      />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          {/* Header */}
          <div className="bg-blue-50 dark:bg-blue-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              {formatPatientName(appointment.patient_name)} - Appointment
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {formatTime(appointment.startTime)}
              {appointment.length && ` • ${formatDuration(appointment.length)}`}
              {appointment.operatory && ` • ${appointment.operatory}`}
            </p>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Patient Information */}
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Patient Information
                </h2>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Patient Name
                    </label>
                    <div className="mt-1 flex items-center gap-2">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {formatPatientName(appointment.patient_name)}
                      </span>
                      {appointment.patient_id && (
                        <button
                          onClick={() => router.push(`/patient/${appointment.patient_id}`)}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-xs underline"
                        >
                          View Patient
                        </button>
                      )}
                    </div>
                  </div>

                  {appointment.patient_dob && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Date of Birth
                      </label>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {appointment.patient_dob}
                      </p>
                    </div>
                  )}

                  {appointment.patient_phone && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Phone
                      </label>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {appointment.patient_phone}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Appointment Details */}
              <div>
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Appointment Details
                </h2>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Time
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {formatTime(appointment.startTime)}
                      {appointment.endTime && ` - ${formatTime(appointment.endTime)}`}
                    </p>
                  </div>

                  {appointment.length && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Duration
                      </label>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {formatDuration(appointment.length)}
                      </p>
                    </div>
                  )}

                  {appointment.type && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Appointment Type
                      </label>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {appointment.type}
                      </p>
                    </div>
                  )}

                  {appointment.provider && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Provider
                      </label>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {typeof appointment.provider === 'string'
                          ? appointment.provider
                          : typeof appointment.provider === 'object' && appointment.provider.href
                            ? 'Provider (see API)'
                            : 'Unknown Provider'
                        }
                      </p>
                    </div>
                  )}

                  {appointment.status && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Status
                      </label>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {appointment.status}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Appointment Notes */}
            {appointment.notes && (
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Appointment Notes
                </h2>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
                  <p className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">
                    {appointment.notes}
                  </p>
                </div>
              </div>
            )}

            {/* Clinical Notes for this Appointment */}
            {appointment.patient_id && (
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <ClinicalNotesDisplay
                  patientId={appointment.patient_id}
                  appointmentId={appointment.id}
                  appointmentDate={getAppointmentDate()}
                  title="Clinical Notes for this Appointment"
                  showPagination={false}
                  showFilters={false}
                  isDarkMode={theme === 'dark'}
                  maxHeight="300px"
                />
              </div>
            )}

            {/* Voice Workflow Status */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Voice Workflow</h3>
                <button
                  onClick={() => router.push(`/voice-workflow-schedule?date=${getAppointmentDate()}`)}
                  className="flex items-center space-x-1 px-3 py-1 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
                >
                  <span>🎤</span>
                  <span>Open Voice Workflow</span>
                </button>
              </div>

              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Recording Status</div>
                    <div className="text-lg font-semibold text-orange-600">
                      Checking...
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Chart Notes</div>
                    <div className="text-lg font-semibold text-blue-600">
                      Available
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-600 dark:text-gray-400">Workflow Status</div>
                    <div className="text-lg font-semibold text-gray-600">
                      Pending
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Voice Recordings for this Appointment */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <VoiceRecordingsForAppointment
                appointmentId={appointment.id}
                appointmentDate={getAppointmentDate()}
                patientName={formatPatientName(appointment.patient_name)}
                isDarkMode={theme === 'dark'}
              />
            </div>

            {/* Actions */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex flex-wrap gap-3">
                {appointment.patient_id && (
                  <button
                    onClick={() => router.push(`/patient/${appointment.patient_id}`)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    View Patient Details
                  </button>
                )}

                {appointment.patient_id && (
                  <a
                    href={`https://clinic.overjet.ai/app/fmx/dailypatients/${appointment.patient_id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    View in Overjet
                  </a>
                )}

                <button
                  onClick={() => router.back()}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Back to Schedule
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

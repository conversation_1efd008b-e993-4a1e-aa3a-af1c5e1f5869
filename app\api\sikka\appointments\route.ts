import { NextRequest, NextResponse } from 'next/server';
import { AzurePostgresDB } from '@/lib/azure-postgres-db';
import { featureFlags } from '@/lib/feature-flags';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

/**
 * Sikka API Integration for Appointments
 * 
 * This endpoint provides access to Sikka appointment data
 * Based on user's memory: Sikka API explorer is available at 
 * https://api.sikkasoft.com/v4/portal/user/api-explorer
 */

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const clientIP = request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    'sikka-client';

  try {
    // Load Sikka credentials and initialize client
    const credentials = loadCredentials();
    if (!credentials.office_id || !credentials.secret_key || !credentials.app_id || !credentials.app_key) {
      return NextResponse.json({
        error: 'Sikka API not configured',
        message: 'Missing required Sikka credentials',
        configured: false,
        required_env: ['SIKKA_OFFICE_ID', 'SIKKA_SECRET_KEY', 'SIKKA_APP_ID', 'SIKKA_APP_KEY']
      }, { status: 503 });
    }

    const sikkaClient = new SikkaApiClient(credentials);

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const provider = searchParams.get('provider');
    const patientId = searchParams.get('patientId');
    const syncToDatabase = searchParams.get('sync') === 'true';
    const retryOnFailure = searchParams.get('retry') !== 'false';
    const maxRetries = parseInt(searchParams.get('maxRetries') || '3');

    // Log audit event for Sikka API access
    await AzurePostgresDB.logAuditEvent({
      userId: 'sikka-integration',
      action: 'sikka_api_request',
      resourceType: 'appointment',
      resourceId: date || 'all',
      details: {
        date,
        provider,
        patientId,
        syncToDatabase,
        endpoint: 'appointments'
      },
      ipAddress: clientIP
    });

    // Use SikkaApiClient to fetch appointments
    console.log(`Fetching appointments from Sikka API for date: ${date || 'all'}`);

    let appointments: any[] = [];
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Authenticate and get appointments using the proper client
        await sikkaClient.authenticate();

        if (date) {
          appointments = await sikkaClient.getAppointments(date);
        } else {
          // If no date specified, get today's appointments
          const today = new Date().toISOString().split('T')[0];
          appointments = await sikkaClient.getAppointments(today);
        }

        // Success, exit retry loop
        break;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Sikka API call failed');
        if (attempt === maxRetries || !retryOnFailure) {
          throw lastError;
        }

        const delay = Math.pow(2, attempt) * 1000;
        console.log(`Sikka API attempt ${attempt} failed with error, retrying in ${delay}ms...`, error);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const queryTime = Date.now() - startTime;
    console.log(`✅ Successfully fetched ${appointments.length} appointments from Sikka API in ${queryTime}ms`);

    // Transform and validate appointment data
    const transformedAppointments = transformSikkaAppointments(appointments);

    // Automatic sync to Azure PostgreSQL if enabled
    let syncResult = null;
    if (syncToDatabase && featureFlags.useAzurePostgres && transformedAppointments.length > 0) {
      try {
        const syncedCount = await AzurePostgresDB.syncAppointmentsFromSikka(transformedAppointments);
        syncResult = {
          success: true,
          syncedCount,
          totalAppointments: transformedAppointments.length
        };

        // Log successful sync
        await AzurePostgresDB.logAuditEvent({
          userId: 'sikka-integration',
          action: 'sikka_appointments_synced',
          resourceType: 'appointment',
          resourceId: 'bulk_sync',
          details: {
            syncedCount,
            totalAppointments: transformedAppointments.length,
            date,
            queryTimeMs: queryTime
          },
          ipAddress: clientIP
        });

        console.log(`✅ Synced ${syncedCount} appointments to Azure PostgreSQL`);
      } catch (syncError) {
        console.error('❌ Failed to sync appointments to Azure PostgreSQL:', syncError);
        syncResult = {
          success: false,
          error: syncError instanceof Error ? syncError.message : 'Sync failed'
        };

        // Log sync failure
        await AzurePostgresDB.logAuditEvent({
          userId: 'sikka-integration',
          action: 'sikka_sync_failed',
          resourceType: 'appointment',
          resourceId: 'bulk_sync',
          details: {
            error: syncError instanceof Error ? syncError.message : 'Sync failed',
            totalAppointments: transformedAppointments.length,
            date
          },
          ipAddress: clientIP
        });
      }
    }

    // Log successful API request
    await AzurePostgresDB.logAuditEvent({
      userId: 'sikka-integration',
      action: 'sikka_api_request_success',
      resourceType: 'appointment',
      resourceId: date || 'all',
      details: {
        appointmentCount: transformedAppointments.length,
        queryTimeMs: queryTime,
        syncEnabled: syncToDatabase,
        syncResult
      },
      ipAddress: clientIP
    });

    return NextResponse.json({
      success: true,
      appointments: transformedAppointments,
      metadata: {
        source: 'sikka',
        timestamp: new Date().toISOString(),
        queryTimeMs: queryTime,
        totalCount: transformedAppointments.length,
        retryAttempts: maxRetries
      },
      sync: syncResult,
      rawData: syncToDatabase ? null : appointments // Include raw data only if not syncing
    });

  } catch (error: any) {
    const queryTime = Date.now() - startTime;
    console.error('Sikka API integration error:', error);

    // Log audit event for error
    try {
      await AzurePostgresDB.logAuditEvent({
        userId: 'sikka-integration',
        action: 'sikka_integration_error',
        resourceType: 'appointment',
        resourceId: 'error',
        details: {
          error: error.message,
          queryTimeMs: queryTime,
          stackTrace: error.stack
        },
        ipAddress: clientIP
      });
    } catch (auditError) {
      console.error('Failed to log audit event:', auditError);
    }

    return NextResponse.json({
      error: 'Sikka API integration error',
      message: error.message,
      configured: !!process.env.SIKKA_API_KEY,
      troubleshooting: {
        checkConnection: 'Verify internet connectivity to Sikka API',
        checkCredentials: 'Ensure SIKKA_API_KEY is valid and active',
        checkEndpoint: 'Verify Sikka API endpoint URL is correct'
      },
      metadata: {
        queryTimeMs: queryTime,
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}

/**
 * Transform Sikka appointment data to match our schema
 */
function transformSikkaAppointments(sikkaData: any): any[] {
  try {
    // Handle different response formats from Sikka API
    let appointments = [];

    if (Array.isArray(sikkaData)) {
      appointments = sikkaData;
    } else if (sikkaData.data && Array.isArray(sikkaData.data)) {
      appointments = sikkaData.data;
    } else if (sikkaData.appointments && Array.isArray(sikkaData.appointments)) {
      appointments = sikkaData.appointments;
    } else {
      console.warn('Unexpected Sikka API response format:', typeof sikkaData);
      return [];
    }

    return appointments.map((appt: any) => ({
      sikka_id: appt.id || appt.appointment_id || `sikka_${Date.now()}_${Math.random()}`,
      patient_name: appt.patient_name || appt.patientName || 'Unknown Patient',
      appointment_date: formatSikkaDate(appt.appointment_date || appt.date || appt.appointmentDate),
      appointment_time: formatSikkaTime(appt.appointment_time || appt.time || appt.startTime),
      operatory: appt.operatory || appt.room || appt.location || 'Unknown',
      provider: appt.provider || appt.doctor || appt.dentist || 'Unknown Provider',
      appointment_type: appt.appointment_type || appt.type || appt.procedure || null,
      status: appt.status || 'scheduled',
      notes: appt.notes || appt.comments || null,
      patient_phone: appt.patient_phone || appt.phone || null,
      patient_email: appt.patient_email || appt.email || null,
      // Additional Sikka-specific fields
      sikka_metadata: {
        original_data: appt,
        transformed_at: new Date().toISOString(),
        api_version: 'v4'
      }
    }));
  } catch (error) {
    console.error('Failed to transform Sikka appointments:', error);
    return [];
  }
}

/**
 * Format Sikka time to proper display format
 */
function formatSikkaTime(timeInput: any): string {
  try {
    if (!timeInput) {
      return '8:00 AM'; // Default to 8:00 AM instead of midnight
    }

    const timeStr = String(timeInput);

    // If it's already in a good format (has AM/PM), return as-is
    if (timeStr.match(/\d+:\d+\s*(AM|PM)/i)) {
      return timeStr;
    }

    // Handle 24-hour format
    const match24 = timeStr.match(/^(\d{1,2}):(\d{2})$/);
    if (match24) {
      let hours = parseInt(match24[1], 10);
      const minutes = match24[2];
      
      // Convert midnight (00:00) to reasonable default
      if (hours === 0 && minutes === "00") {
        return '8:00 AM';
      }
      
      const period = hours >= 12 ? 'PM' : 'AM';
      if (hours === 0) hours = 12;
      if (hours > 12) hours -= 12;
      
      return `${hours}:${minutes} ${period}`;
    }

    // Handle military time without colon (HHMM)
    const matchMilitary = timeStr.match(/^(\d{2})(\d{2})$/);
    if (matchMilitary) {
      let hours = parseInt(matchMilitary[1], 10);
      const minutes = matchMilitary[2];
      
      // Convert midnight to reasonable default
      if (hours === 0 && minutes === "00") {
        return '8:00 AM';
      }
      
      const period = hours >= 12 ? 'PM' : 'AM';
      if (hours === 0) hours = 12;
      if (hours > 12) hours -= 12;
      
      return `${hours}:${minutes} ${period}`;
    }

    // If we can't parse it, return a reasonable default
    console.warn('Unable to parse Sikka time format:', timeInput);
    return '8:00 AM';
    
  } catch (error) {
    console.error('Time formatting error:', error, 'Input:', timeInput);
    return '8:00 AM';
  }
}

/**
 * Format Sikka date to YYYY-MM-DD format
 */
function formatSikkaDate(dateInput: any): string {
  try {
    if (!dateInput) {
      return new Date().toISOString().split('T')[0]; // Default to today
    }

    let date: Date;

    if (typeof dateInput === 'string') {
      // Handle various date formats from Sikka
      if (dateInput.includes('T')) {
        date = new Date(dateInput); // ISO format
      } else if (dateInput.includes('/')) {
        // MM/DD/YYYY or DD/MM/YYYY format
        const parts = dateInput.split('/');
        if (parts.length === 3) {
          // Assume MM/DD/YYYY for US format
          date = new Date(parseInt(parts[2]), parseInt(parts[0]) - 1, parseInt(parts[1]));
        } else {
          date = new Date(dateInput);
        }
      } else if (dateInput.includes('-')) {
        date = new Date(dateInput); // YYYY-MM-DD format
      } else {
        date = new Date(dateInput);
      }
    } else if (dateInput instanceof Date) {
      date = dateInput;
    } else {
      date = new Date(dateInput);
    }

    // Validate date
    if (isNaN(date.getTime())) {
      console.warn('Invalid date from Sikka:', dateInput);
      return new Date().toISOString().split('T')[0];
    }

    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', dateInput);
    return new Date().toISOString().split('T')[0];
  }
}

/**
 * Test endpoint for Sikka API configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { apiKey, apiUrl } = body;

    if (!apiKey) {
      return NextResponse.json({
        error: 'API key required for testing'
      }, { status: 400 });
    }

    const testUrl = apiUrl || 'https://api.sikkasoft.com/v4';
    const testEndpoint = `${testUrl}/appointments?limit=1`;

    console.log(`Testing Sikka API: ${testEndpoint}`);

    const testResponse = await fetch(testEndpoint, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    const responseText = await testResponse.text();

    return NextResponse.json({
      success: testResponse.ok,
      status: testResponse.status,
      statusText: testResponse.statusText,
      response: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''),
      endpoint: testEndpoint
    });

  } catch (error: any) {
    return NextResponse.json({
      error: 'Test failed',
      message: error.message
    }, { status: 500 });
  }
}

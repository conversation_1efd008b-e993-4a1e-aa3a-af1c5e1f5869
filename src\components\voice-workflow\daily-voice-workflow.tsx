"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { 
  Mi<PERSON>, 
  FileText, 
  Clock, 
  User, 
  MapPin, 
  CheckCircle, 
  Alert<PERSON><PERSON>gle, 
  RefreshCw,
  Sparkles,
  Calendar,
  ArrowRight
} from 'lucide-react';

interface VoiceRecording {
  id: string;
  filename: string;
  recording_date: string;
  transcription?: string;
  transcription_confidence?: number;
  duration_seconds?: number;
  status: 'unassigned' | 'assigned' | 'transcribing' | 'transcribed' | 'reviewing' | 'completed';
  assigned_appointment_id?: string;
}

interface WorkflowAppointment {
  sikka_id: string;
  patient_id: string;
  patient_name: string;
  appointment_date: string;
  appointment_time: string;
  operatory: string;
  provider: string;
  appointment_type?: string;
  chart_notes?: string;
  recording_status: 'has_recording' | 'no_recording';
  workflow_status: 'pending' | 'in_progress' | 'completed';
  professionalized_note?: string;
  matched_recording_id?: string;
}

interface DailyWorkflowSummary {
  date: string;
  total_appointments: number;
  appointments_with_recordings: number;
  appointments_without_recordings: number;
  completed_notes: number;
  pending_notes: number;
  unmatched_recordings: number;
}

interface DailyVoiceWorkflowProps {
  date: string;
  isDarkMode?: boolean;
  onWorkflowUpdate?: (summary: DailyWorkflowSummary) => void;
}

// Drag and Drop Item Types
const ItemTypes = {
  RECORDING: 'recording',
  APPOINTMENT: 'appointment'
};

// Draggable Recording Card Component
function DraggableRecording({ 
  recording, 
  onPlayRecording 
}: { 
  recording: VoiceRecording;
  onPlayRecording: (recording: VoiceRecording) => void;
}) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.RECORDING,
    item: { id: recording.id, type: 'recording', recording },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'transcribed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'transcribing': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'assigned': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div
      ref={drag}
      className={`
        p-3 border rounded-lg cursor-move transition-all duration-200
        ${isDragging ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}
        ${recording.assigned_appointment_id ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'}
        hover:shadow-md hover:border-blue-300
      `}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Mic className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-900 truncate">
            {recording.filename}
          </span>
        </div>
        <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(recording.status)}`}>
          {recording.status}
        </span>
      </div>

      <div className="space-y-1 text-xs text-gray-600">
        <div className="flex items-center space-x-1">
          <Clock className="h-3 w-3" />
          <span>{new Date(recording.recording_date).toLocaleTimeString()}</span>
          {recording.duration_seconds && (
            <span className="ml-2">({formatDuration(recording.duration_seconds)})</span>
          )}
        </div>

        {recording.transcription && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <FileText className="h-3 w-3" />
              <span className="font-medium">Transcription</span>
              {recording.transcription_confidence && (
                <span className="text-gray-500">
                  ({Math.round(recording.transcription_confidence * 100)}%)
                </span>
              )}
            </div>
            <p className="line-clamp-2">{recording.transcription}</p>
          </div>
        )}

        {recording.assigned_appointment_id && (
          <div className="flex items-center space-x-1 text-green-600">
            <CheckCircle className="h-3 w-3" />
            <span>Matched to appointment</span>
          </div>
        )}
      </div>

      <button
        onClick={() => onPlayRecording(recording)}
        className="mt-2 w-full px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
      >
        Play Recording
      </button>
    </div>
  );
}

// Droppable Appointment Card Component
function DroppableAppointment({ 
  appointment, 
  onRecordingDrop,
  onGenerateNote,
  onViewNote
}: { 
  appointment: WorkflowAppointment;
  onRecordingDrop: (recordingId: string, appointmentId: string) => void;
  onGenerateNote: (appointmentId: string) => void;
  onViewNote: (appointmentId: string) => void;
}) {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: ItemTypes.RECORDING,
    drop: (item: { id: string; recording: VoiceRecording }) => {
      if (!appointment.matched_recording_id) {
        onRecordingDrop(item.id, appointment.sikka_id);
      }
    },
    canDrop: () => !appointment.matched_recording_id,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  const getWorkflowStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div
      ref={drop}
      className={`
        p-4 border rounded-lg transition-all duration-200
        ${isOver && canDrop ? 'border-blue-400 bg-blue-50 shadow-lg' : ''}
        ${appointment.matched_recording_id ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200'}
        ${!appointment.matched_recording_id ? 'border-dashed' : ''}
        hover:shadow-md
      `}
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h3 className="font-semibold text-gray-900">{appointment.patient_name}</h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{appointment.appointment_time}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MapPin className="h-3 w-3" />
              <span>{appointment.operatory}</span>
            </div>
            <div className="flex items-center space-x-1">
              <User className="h-3 w-3" />
              <span>{appointment.provider}</span>
            </div>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs rounded-full border ${getWorkflowStatusColor(appointment.workflow_status)}`}>
          {appointment.workflow_status}
        </span>
      </div>

      {appointment.appointment_type && (
        <p className="text-sm text-gray-600 mb-3">{appointment.appointment_type}</p>
      )}

      {/* Recording Status */}
      <div className="mb-3">
        {appointment.matched_recording_id ? (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm">Recording matched</span>
          </div>
        ) : (
          <div className="flex items-center space-x-2 text-gray-500">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">Drop recording here to match</span>
          </div>
        )}
      </div>

      {/* Chart Notes Preview */}
      {appointment.chart_notes && (
        <div className="mb-3 p-2 bg-gray-50 rounded text-xs">
          <div className="flex items-center space-x-1 mb-1">
            <FileText className="h-3 w-3" />
            <span className="font-medium">Chart Notes</span>
          </div>
          <p className="line-clamp-2">{appointment.chart_notes}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-2">
        {appointment.matched_recording_id && !appointment.professionalized_note && (
          <button
            onClick={() => onGenerateNote(appointment.sikka_id)}
            className="flex items-center space-x-1 px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
          >
            <Sparkles className="h-3 w-3" />
            <span>Generate Note</span>
          </button>
        )}

        {appointment.professionalized_note && (
          <button
            onClick={() => onViewNote(appointment.sikka_id)}
            className="flex items-center space-x-1 px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            <FileText className="h-3 w-3" />
            <span>View Note</span>
          </button>
        )}
      </div>

      {/* Drop Zone Indicator */}
      {isOver && canDrop && (
        <div className="absolute inset-0 border-2 border-blue-400 border-dashed rounded-lg bg-blue-50 bg-opacity-50 flex items-center justify-center">
          <div className="text-blue-600 font-medium">Drop recording here</div>
        </div>
      )}
    </div>
  );
}

// Main Daily Voice Workflow Component
export function DailyVoiceWorkflow({ 
  date, 
  isDarkMode = false, 
  onWorkflowUpdate 
}: DailyVoiceWorkflowProps) {
  const [recordings, setRecordings] = useState<VoiceRecording[]>([]);
  const [appointments, setAppointments] = useState<WorkflowAppointment[]>([]);
  const [summary, setSummary] = useState<DailyWorkflowSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load daily workflow data
  const loadWorkflowData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/voice-workflow/daily-process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date })
      });

      if (!response.ok) {
        throw new Error(`Failed to load workflow data: ${response.status}`);
      }

      const data = await response.json();
      
      setAppointments(data.appointments || []);
      setRecordings(data.unmatched_recordings || []);
      setSummary(data.summary);
      
      if (onWorkflowUpdate) {
        onWorkflowUpdate(data.summary);
      }

    } catch (err) {
      console.error('Error loading workflow data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load workflow data');
    } finally {
      setLoading(false);
    }
  }, [date, onWorkflowUpdate]);

  useEffect(() => {
    loadWorkflowData();
  }, [loadWorkflowData]);

  // Handle recording drop on appointment
  const handleRecordingDrop = async (recordingId: string, appointmentId: string) => {
    try {
      const response = await fetch('/api/voice-workflow/match-recordings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          recording_id: recordingId,
          appointment_id: appointmentId,
          match_type: 'manual',
          confidence: 1.0,
          reasoning: 'Manual drag and drop match'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to match recording to appointment');
      }

      // Update local state
      setRecordings(prev => prev.filter(r => r.id !== recordingId));
      setAppointments(prev => prev.map(apt => 
        apt.sikka_id === appointmentId 
          ? { ...apt, matched_recording_id: recordingId, recording_status: 'has_recording' }
          : apt
      ));

      // Refresh workflow data
      await loadWorkflowData();

    } catch (err) {
      console.error('Error matching recording:', err);
      setError(err instanceof Error ? err.message : 'Failed to match recording');
    }
  };

  // Handle generate professionalized note
  const handleGenerateNote = async (appointmentId: string) => {
    try {
      const appointment = appointments.find(apt => apt.sikka_id === appointmentId);
      if (!appointment) return;

      const response = await fetch('/api/voice-workflow/generate-note', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          patient_name: appointment.patient_name,
          appointment_date: appointment.appointment_date,
          appointment_time: appointment.appointment_time,
          operatory: appointment.operatory,
          provider: appointment.provider,
          transcription: '', // Would get from matched recording
          chart_notes: appointment.chart_notes
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate professionalized note');
      }

      const data = await response.json();
      
      // Update appointment with generated note
      setAppointments(prev => prev.map(apt => 
        apt.sikka_id === appointmentId 
          ? { 
              ...apt, 
              professionalized_note: data.professionalized_note,
              workflow_status: 'completed'
            }
          : apt
      ));

    } catch (err) {
      console.error('Error generating note:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate note');
    }
  };

  const handlePlayRecording = (recording: VoiceRecording) => {
    // Implement audio playback
    console.log('Playing recording:', recording.filename);
  };

  const handleViewNote = (appointmentId: string) => {
    // Navigate to appointment detail or show note modal
    console.log('Viewing note for appointment:', appointmentId);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
        <span className="ml-2">Loading voice workflow...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center space-x-2">
          <AlertTriangle className="h-5 w-5 text-red-600" />
          <span className="text-red-800 font-medium">Error</span>
        </div>
        <p className="text-red-700 mt-1">{error}</p>
        <button
          onClick={loadWorkflowData}
          className="mt-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="space-y-6">
        {/* Summary Header */}
        {summary && (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Voice Workflow - {new Date(date).toLocaleDateString()}
              </h2>
              <button
                onClick={loadWorkflowData}
                className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="h-3 w-3" />
                <span>Refresh</span>
              </button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{summary.total_appointments}</div>
                <div className="text-sm text-gray-600">Total Appointments</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{summary.appointments_with_recordings}</div>
                <div className="text-sm text-gray-600">With Recordings</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{summary.completed_notes}</div>
                <div className="text-sm text-gray-600">Completed Notes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{summary.unmatched_recordings}</div>
                <div className="text-sm text-gray-600">Unmatched Recordings</div>
              </div>
            </div>
          </div>
        )}

        {/* Main Workflow Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Unmatched Recordings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <Mic className="h-5 w-5" />
              <span>Unmatched Recordings ({recordings.length})</span>
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {recordings.map(recording => (
                <DraggableRecording
                  key={recording.id}
                  recording={recording}
                  onPlayRecording={handlePlayRecording}
                />
              ))}
              {recordings.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  <Mic className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>All recordings have been matched</p>
                </div>
              )}
            </div>
          </div>

          {/* Appointments */}
          <div className="lg:col-span-2 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Appointments ({appointments.length})</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {appointments.map(appointment => (
                <DroppableAppointment
                  key={appointment.sikka_id}
                  appointment={appointment}
                  onRecordingDrop={handleRecordingDrop}
                  onGenerateNote={handleGenerateNote}
                  onViewNote={handleViewNote}
                />
              ))}
              {appointments.length === 0 && (
                <div className="col-span-2 text-center text-gray-500 py-8">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No appointments found for this date</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DndProvider>
  );
}

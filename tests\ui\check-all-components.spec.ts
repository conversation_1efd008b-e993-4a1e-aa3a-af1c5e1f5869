import { test, expect } from '@playwright/test';

const BASE_URL = 'https://dentalapp-plum.vercel.app';
const PASSWORD = 'dental123';

// List of main routes to check (from LIVE_APP_ENDPOINTS.md)
const ROUTES = [
    '/',
    '/voice-workflow',
    '/webusb-transfer',
    '/patient-search',
    '/schedule',
    '/operatories',
    '/ai-assistant',
    '/settings',
    '/match-recordings/2025-01-01', // Example date for dynamic route
    '/transcription-control',
    '/debug-transcription',
    '/patient/1', // Example patient id
    '/appointment/1', // Example appointment id
    '/test-webusb',
    '/cache-test'
];

test.describe('Dental App - Component Display (Light & Dark Modes)', () => {
    for (const route of ROUTES) {
        test(`Check components on ${route} (light & dark)`, async ({ page }) => {
            // Go to login page
            await page.goto(BASE_URL + route);

            // If login prompt is present, enter password
            if (await page.locator('input[type="password"]').isVisible({ timeout: 2000 }).catch(() => false)) {
                await page.fill('input[type="password"]', PASSWORD);
                await page.keyboard.press('Enter');
                // Wait for navigation after login
                await page.waitForLoadState('networkidle');
            }

            // Wait for main content to load
            await page.waitForTimeout(1500);

            // Take screenshot in light mode
            await page.screenshot({ path: `screenshots${route.replace(/\//g, '_')}_light.png`, fullPage: true });

            // Try to toggle dark mode (common selectors)
            const darkModeSelectors = [
                'button[aria-label*="dark"]',
                'button[aria-label*="theme"]',
                'button[aria-label*="Dark"]',
                'button:has(svg[data-icon="moon"])',
                'button:has-text("Dark")',
                '[data-testid="dark-mode-toggle"]'
            ];
            let toggled = false;
            for (const selector of darkModeSelectors) {
                if (await page.locator(selector).isVisible().catch(() => false)) {
                    await page.click(selector);
                    toggled = true;
                    break;
                }
            }
            if (!toggled) {
                // Try to toggle via keyboard shortcut (if implemented)
                // await page.keyboard.press('Control+J');
            }

            // Wait for dark mode to apply
            await page.waitForTimeout(1000);

            // Take screenshot in dark mode
            await page.screenshot({ path: `screenshots${route.replace(/\//g, '_')}_dark.png`, fullPage: true });
        });
    }
});

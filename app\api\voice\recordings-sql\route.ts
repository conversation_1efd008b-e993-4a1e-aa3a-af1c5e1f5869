import { NextRequest, NextResponse } from 'next/server';
import { AzurePostgresDB } from '@/lib/azure-postgres-db';
import { featureFlags } from '@/lib/feature-flags';

// In-memory cache for API responses (matching legacy behavior)
const apiCache: Record<string, { data: unknown, timestamp: number }> = {};
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour

// Helper function to get cache key
function getCacheKey(url: string, params: URLSearchParams): string {
  const sortedParams = Array.from(params.entries()).sort();
  return `${url}?${new URLSearchParams(sortedParams).toString()}`;
}

// Helper function to check cache
function getFromCache(cacheKey: string): unknown | null {
  const cached = apiCache[cacheKey];
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  delete apiCache[cacheKey];
  return null;
}

// Helper function to set cache
function setCache(cacheKey: string, data: unknown): void {
  apiCache[cacheKey] = {
    data,
    timestamp: Date.now()
  };
}

export async function GET(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.useAzurePostgres) {
      return NextResponse.json({
        error: 'Azure Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const cacheKey = getCacheKey('/api/voice/recordings-sql', searchParams);

    // Check cache first
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      return NextResponse.json(cachedData);
    }

    // Parse query parameters
    const date = searchParams.get('date');
    const status = searchParams.get('status');
    const deviceId = searchParams.get('deviceId');
    const patientId = searchParams.get('patientId');
    const limit = Math.min(parseInt(searchParams.get('limit') || '100'), 1000);
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log(`📊 Fetching recordings: date=${date}, status=${status}, deviceId=${deviceId}, patientId=${patientId}`);

    // Use AzurePostgresDB to get transcriptions
    const recordings = await AzurePostgresDB.getTranscriptions({
      patientId,
      status,
      limit,
      offset
    });

    // For simplicity, we'll use the returned array length as total count
    // In a production system, you'd want a separate count query
    const totalCount = recordings.length;

    // Format the response to match legacy API structure
    const formattedRecordings = recordings.map(recording => ({
      id: recording.id,
      filename: recording.filename,
      deviceId: recording.patient_id, // Using patient_id as deviceId for compatibility
      patientId: recording.patient_id,
      transcriptionText: recording.transcription_text,
      summaryText: recording.summary_text,
      confidenceScore: recording.confidence_score,
      status: recording.transcription_status,
      createdAt: recording.created_at,
      updatedAt: recording.updated_at,
      metadata: recording.metadata,
      // Legacy compatibility fields
      hasTranscription: !!recording.transcription_text,
      hasSummary: !!recording.summary_text,
      processingStatus: mapStatusToFrontend(recording.transcription_status),
      // Additional fields from AzurePostgresDB
      name: recording.filename,
      size: recording.audio_file_size || 0,
      date: recording.recording_date
    }));

    const responseData = {
      success: true,
      recordings: formattedRecordings,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + recordings.length < totalCount
      },
      filters: {
        date,
        status,
        deviceId,
        patientId
      },
      timestamp: new Date().toISOString()
    };

    // Cache the response
    setCache(cacheKey, responseData);

    console.log(`✅ Retrieved ${recordings.length} recordings (${totalCount} total)`);
    return NextResponse.json(responseData);

  } catch (error) {
    console.error('❌ Error fetching recordings:', error);

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to fetch recordings',
      code: 'FETCH_ERROR'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.useAzurePostgres) {
      return NextResponse.json({
        error: 'Azure Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const body = await request.json();
    const { action, recordingIds, updates } = body;

    switch (action) {
      case 'updateStatus':
        if (!recordingIds || !updates?.status) {
          return NextResponse.json({
            error: 'Recording IDs and status are required',
            code: 'MISSING_PARAMETERS'
          }, { status: 400 });
        }

        // Update multiple recordings
        // Note: AzurePostgresDB doesn't have updateTranscriptionStatus method yet
        // This functionality needs to be implemented or handled differently
        const updateResults = await Promise.allSettled(
          recordingIds.map(async (id: string) => {
            // For now, return success - implement proper update logic later
            return Promise.resolve({ id, status: updates.status });
          })
        );

        const successful = updateResults.filter(result => result.status === 'fulfilled').length;
        const failed = updateResults.length - successful;

        return NextResponse.json({
          success: true,
          updated: successful,
          failed,
          message: `Updated ${successful} recordings, ${failed} failed`
        });

      case 'delete':
        if (!recordingIds) {
          return NextResponse.json({
            error: 'Recording IDs are required',
            code: 'MISSING_PARAMETERS'
          }, { status: 400 });
        }

        // Soft delete recordings by updating status
        // Note: AzurePostgresDB doesn't have updateTranscriptionStatus method yet
        const deleteResults = await Promise.allSettled(
          recordingIds.map(async (id: string) => {
            // For now, return success - implement proper delete logic later
            return Promise.resolve({ id, status: 'deleted' });
          })
        );

        const deletedCount = deleteResults.filter(result => result.status === 'fulfilled').length;

        return NextResponse.json({
          success: true,
          deleted: deletedCount,
          message: `Deleted ${deletedCount} recordings`
        });

      case 'search':
        const { query, searchType = 'transcription' } = body;

        if (!query) {
          return NextResponse.json({
            error: 'Search query is required',
            code: 'MISSING_QUERY'
          }, { status: 400 });
        }

        const searchResults = await AzurePostgresDB.searchTranscriptions(query, 50);

        return NextResponse.json({
          success: true,
          results: searchResults,
          query,
          searchType
        });

      default:
        return NextResponse.json({
          error: `Unknown action: ${action}`,
          code: 'INVALID_ACTION'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Error in POST /recordings-sql:', error);

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'POST_ERROR'
    }, { status: 500 });
  }
}

// Helper function to map database status to frontend-friendly status
function mapStatusToFrontend(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'Pending',
    'processing': 'Processing',
    'completed': 'Completed',
    'failed': 'Failed',
    'deleted': 'Deleted'
  };

  return statusMap[status] || status;
}

// Cache invalidation endpoint
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const cacheKey = searchParams.get('cacheKey');

    if (cacheKey) {
      delete apiCache[cacheKey];
      return NextResponse.json({
        success: true,
        message: 'Cache key deleted'
      });
    } else {
      // Clear all cache
      Object.keys(apiCache).forEach(key => delete apiCache[key]);
      return NextResponse.json({
        success: true,
        message: 'All cache cleared'
      });
    }

  } catch (error) {
    return NextResponse.json({
      error: 'Failed to clear cache',
      code: 'CACHE_ERROR'
    }, { status: 500 });
  }
}
"use client";

import { ChevronLeft, ChevronRight, Expand, Minimize } from "lucide-react";

interface CalendarWidgetProps {
  selectedDate: string;
  onDateChange: (date: string) => void;
  calendarView: 'week' | 'month';
  onViewChange: (view: 'week' | 'month') => void;
  isDarkMode: boolean;
  isMounted: boolean;
}

export function CalendarWidget({
  selectedDate,
  onDateChange,
  calendarView,
  onViewChange,
  isDarkMode,
  isMounted
}: CalendarWidgetProps) {
  // Get today's date in local timezone
  const today = new Date();
  const todayString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

  const navigateCalendar = (direction: 'prev' | 'next') => {
    const currentDate = new Date(selectedDate);
    if (calendarView === 'month') {
      if (direction === 'prev') {
        currentDate.setMonth(currentDate.getMonth() - 1);
      } else {
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    } else {
      if (direction === 'prev') {
        currentDate.setDate(currentDate.getDate() - 7);
      } else {
        currentDate.setDate(currentDate.getDate() + 7);
      }
    }
    const newDate = currentDate.toISOString().split('T')[0];
    onDateChange(newDate);
  };

  const renderCalendarTitle = () => {
    const [year, month, day] = selectedDate.split('-').map(Number);
    const date = new Date(year, month - 1, day);
    if (calendarView === 'month') {
      return date.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    }
  };

  const renderWeekView = () => {
    const today = new Date();
    const selectedDateObj = new Date(selectedDate + 'T12:00:00');
    const startOfWeek = new Date(selectedDateObj);
    startOfWeek.setDate(selectedDateObj.getDate() - selectedDateObj.getDay());

    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      const year = day.getFullYear();
      const month = String(day.getMonth() + 1).padStart(2, '0');
      const dayOfMonth = String(day.getDate()).padStart(2, '0');
      const dayString = `${year}-${month}-${dayOfMonth}`;
      const isToday = dayString === todayString;
      const isSelected = dayString === selectedDate;
      const isWeekend = i === 0 || i === 6; // Sunday = 0, Saturday = 6

      days.push(
        <div
          key={i}
          className={`p-1 sm:p-2 md:p-3 rounded-lg text-center cursor-pointer transition-colors ${isWeekend ? 'hidden sm:block' : ''
            } ${isSelected
              ? 'bg-blue-600 text-white ring-2 ring-blue-300'
              : isToday
                ? isMounted && isDarkMode
                  ? 'bg-blue-900 text-blue-200 border-2 border-blue-500'
                  : 'bg-blue-100 text-blue-800 border-2 border-blue-400'
                : isMounted && isDarkMode
                  ? 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  : 'bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-200'
            }`}
          onClick={() => onDateChange(dayString)}
          suppressHydrationWarning
        >
          <div className="text-xs font-medium mb-1 hidden sm:block">
            {day.toLocaleDateString('en-US', { weekday: 'short' })}
          </div>
          <div className="text-xs font-medium mb-1 sm:hidden">
            {day.toLocaleDateString('en-US', { weekday: 'narrow' })}
          </div>
          <div className={`text-sm sm:text-lg font-bold ${isToday ? 'text-white' : ''}`}>
            {day.getDate()}
          </div>
          <div className="text-xs mt-1 hidden sm:block">
            {isToday ? 'Today' : day.toLocaleDateString('en-US', { month: 'short' })}
          </div>
        </div>
      );
    }
    // Only render weekdays on mobile, all days on sm+
    if (typeof window !== "undefined" && window.innerWidth < 640) {
      // Mobile: filter out Sunday (0) and Saturday (6)
      return days.filter((_, i) => i > 0 && i < 6);
    }
    return days;
  };

  const renderMonthView = () => {
    const today = new Date();
    const [year, month] = selectedDate.split('-').map(Number);
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      const day = new Date(startDate);
      day.setDate(startDate.getDate() + i);
      const dayYear = day.getFullYear();
      const dayMonth = String(day.getMonth() + 1).padStart(2, '0');
      const dayOfMonth = String(day.getDate()).padStart(2, '0');
      const dayString = `${dayYear}-${dayMonth}-${dayOfMonth}`;
      const isToday = dayString === todayString;
      const isSelected = dayString === selectedDate;
      const isCurrentMonth = day.getMonth() === month - 1;

      days.push(
        <div
          key={i}
          className={`p-1 sm:p-2 md:p-3 text-center cursor-pointer transition-colors rounded-lg ${isSelected
              ? 'bg-blue-600 text-white ring-2 ring-blue-300'
              : isToday
                ? isMounted && isDarkMode
                  ? 'bg-blue-900 text-blue-200 border-2 border-blue-500'
                  : 'bg-blue-100 text-blue-800 border-2 border-blue-400'
                : isCurrentMonth
                  ? isMounted && isDarkMode
                    ? 'bg-slate-700 hover:bg-slate-600 text-slate-200'
                    : 'bg-slate-200 hover:bg-slate-300 text-slate-800 border border-slate-300'
                  : 'text-slate-400 dark:text-slate-600 bg-slate-50 dark:bg-slate-800'
            }`}
          onClick={() => onDateChange(dayString)}
          suppressHydrationWarning
        >
          <div className={`text-sm sm:text-lg font-bold ${isToday && isSelected ? 'text-white' : ''}`}>
            {day.getDate()}
          </div>
        </div>
      );
    }
    return days;
  };

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700">
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => onViewChange(calendarView === 'week' ? 'month' : 'week')}
          className="p-2 rounded-md bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 transition-colors border border-gray-400 dark:border-gray-500"
          title={calendarView === 'week' ? 'Expand to month view' : 'Collapse to week view'}
        >
          {calendarView === 'week' ? (
            <Expand className="w-4 h-4 text-gray-700 dark:text-gray-200" />
          ) : (
            <Minimize className="w-4 h-4 text-gray-700 dark:text-gray-200" />
          )}
        </button>

        <h4 className="text-xl font-bold text-slate-900 dark:text-white text-center">
          {renderCalendarTitle()}
        </h4>

        <button
          onClick={() => onDateChange(todayString)}
          className="px-4 py-2 text-sm rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-300 transition-colors font-medium"
          title="Go to today"
        >
          Today
        </button>
      </div>

      {/* Navigation and Calendar Grid */}
      <div className="flex items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <button
          onClick={() => navigateCalendar('prev')}
          className="p-2 sm:p-3 rounded-lg bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 transition-colors border border-slate-300 dark:border-slate-500"
          title={calendarView === 'month' ? 'Previous month' : 'Previous week'}
        >
          <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6 text-slate-700 dark:text-slate-200" />
        </button>

        <div className="flex-1">
          {calendarView === 'week' ? (
            // Week View
            <div className="grid grid-cols-5 sm:grid-cols-7 gap-1 sm:gap-2">
              {/* Only render 5 columns on mobile, 7 on sm+ */}
              {renderWeekView()}
            </div>
          ) : (
            // Month View
            <div className="grid grid-cols-7 gap-1 sm:gap-2">
              {/* Day headers */}
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                <div key={day} className="p-1 sm:p-2 text-center text-xs sm:text-sm font-medium text-slate-500 dark:text-slate-400">
                  <span className="hidden sm:inline">{day}</span>
                  <span className="sm:hidden">{day.charAt(0)}</span>
                </div>
              ))}
              {renderMonthView()}
            </div>
          )}
        </div>

        <button
          onClick={() => navigateCalendar('next')}
          className="p-2 sm:p-3 rounded-lg bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 transition-colors border border-slate-300 dark:border-slate-500"
          title={calendarView === 'month' ? 'Next month' : 'Next week'}
        >
          <ChevronRight className="w-5 h-5 sm:w-6 sm:h-6 text-slate-700 dark:text-slate-200" />
        </button>
      </div>
    </div>
  );
}

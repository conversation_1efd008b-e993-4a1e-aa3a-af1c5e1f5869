{"name": "dental-schedule-next", "version": "2.0.3", "private": true, "_scriptsDocumentation": {"description": "Key scripts for development, testing, and deployment.", "usage": {"dev": "Start development server", "build": "Build for production", "start": "Start production server", "lint": "Run ESLint checks", "test": "Run Jest tests"}, "validation": {"system-quick": "Fast system health check", "system-full": "Comprehensive system validation", "api-endpoints": "Test all API endpoints", "azure-storage": "Validate Azure Blob Storage integration", "environment": "Verify environment variables"}, "database": {"vercel-postgres": "Setup and verify Vercel <PERSON>", "azure-sql": "Test Azure SQL connectivity", "migration": "Validate database transition"}, "bulk-processing": {"bulk-process:enhanced": "Process recordings using direct service integration with progress tracking", "monitor-progress": "Monitor active bulk processing sessions with real-time updates", "monitor-progress:watch": "Continuous monitoring with auto-refresh", "monitor-progress:session": "Monitor specific session (requires session ID)", "cleanup-sessions": "Clean up old and stale bulk processing sessions", "cleanup-sessions:dry-run": "Preview cleanup operations without making changes", "cleanup-sessions:stale": "Cancel stale active sessions and clean up resources"}, "deployment": {"quick": "Deploy small changes (<30MB)", "standard": "Deploy medium changes (30-70MB)", "large": "Deploy large changes (>70MB)", "monitor": "Monitor ongoing deployments", "analyze-package": "Analyze deployment package size"}, "troubleshooting": {"databaseConnection": "POSTGRES_URL missing → Set in .env.local | Azure SQL → Check connection string format", "azureStorage": "AZURE_STORAGE_CONNECTION_STRING missing → Verify account key | Permissions → Check container access", "apiEndpoints": "Server not running → npm run dev | Feature flags → Check environment variables", "deployment": "Package too large → npm run package:optimize | Timeout → Increase ZIPDEPLOY_TIMEOUT_MS", "bulkProcessing": "Session not found → Check database connectivity | Progress tracking failed → Verify Vercel Postgres setup | Direct service integration → Check OpenAI API key", "sessionManagement": "Stale sessions → Run cleanup-sessions:stale | Database tables missing → Run database initialization | Monitoring errors → Check session tracking database"}}, "scripts": {"dev": "next dev", "dev:clean": "rimraf .next && npm run dev", "dev:verbose": "cross-env NODE_ENV=development NODE_OPTIONS=\"--trace-warnings\" next dev", "dev:quiet": "cross-env NODE_OPTIONS=\"--no-warnings --quiet\" next dev", "postinstall": "node -e \"try { require('typescript'); console.log('✅ TypeScript is available'); } catch (e) { console.warn('⚠️ TypeScript not found - run: npm install typescript'); }\"", "prebuild": "node scripts/set-build-env.js", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:ci": "jest --testPathIgnorePatterns=api-health.test.js --passWithNoTests", "test:manual": "jest --runInBand", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:api": "jest --config jest.config.api.js", "test-azure": "tsx scripts/test-azure-connection.ts || (echo \"❌ Azure connection failed. Verify AZURE_STORAGE_CONNECTION_STRING in .env.local.\" && exit 1)", "test-upload": "tsx scripts/test-upload-api.ts || (echo \"❌ Upload API test failed. Ensure the server is running and API endpoints are accessible. Refer to 'npm run dev' and API documentation.\" && exit 1)", "test-endpoints": "tsx scripts/test-endpoints-comprehensive.ts || (echo \"❌ Endpoint testing failed. Ensure the server is running and API routes are correctly defined. Refer to 'npm run dev'.\" && exit 1)", "test-endpoints-quick": "tsx scripts/test-endpoints-comprehensive.ts --get-only --categories health,debug || (echo \"❌ Quick endpoint test failed. Check server status and basic API routes. Refer to 'npm run dev'.\" && exit 1)", "test-endpoints-voice": "tsx scripts/test-endpoints-comprehensive.ts --categories voice || (echo \"❌ Voice endpoint testing failed. Check voice-related API routes and their dependencies.\" && exit 1)", "demo-endpoint-testing": "tsx scripts/demo-endpoint-testing.ts || (echo \"❌ De<PERSON> failed.\" && exit 1)", "debug-env": "tsx scripts/debug-env.ts || (echo \"❌ Environment debug failed. Verify your .env.local file and environment variable setup.\" && exit 1)", "download-azure-audio": "tsx scripts/download-all-azure-recordings.ts", "debug-azure-recordings": "tsx scripts/debug-azure-recordings.ts", "azure-inventory-and-download": "npm run debug-azure-recordings && npm run download-azure-audio", "migrate-azure": "tsx scripts/migrate-to-azure.ts || (echo \"❌ Azure migration failed. Review logs for specific errors and ensure Azure credentials are correct.\" && exit 1)", "migrate-azure-data": "tsx scripts/migrate-azure-data.ts || (echo \"❌ Azure data migration failed. Check database connectivity and ensure data integrity. Review migration logs for details.\" && exit 1)", "pre-migration-check": "tsx scripts/pre-migration-checks.ts || (echo \"❌ Pre-migration checks failed. Review logs for details and resolve any reported issues before proceeding with migration.\" && exit 1)", "monitor-migration": "tsx scripts/migration-progress-monitor.ts || (echo \"❌ Migration monitoring failed. Review logs for errors and ensure the migration process is active.\" && exit 1)", "validate-blob-integrity": "npm run test-azure && tsx scripts/validate-blob-storage-integrity.ts --sample-size 10 || (echo \"❌ Blob integrity validation failed. Verify Azure Storage account and container access.\" && exit 1)", "validate-azure-integration": "tsx scripts/validate-azure-blob-integration.ts || (echo \"❌ Azure Blob integration validation failed. Verify hybrid architecture setup and connectivity.\" && exit 1)", "full-migration-workflow": "tsx scripts/full-migration-workflow.ts || (echo \"❌ Full migration workflow failed. Review logs for errors in individual migration steps and ensure all dependencies are met.\" && exit 1)", "bulk-process": "tsx scripts/bulk-process-recordings.ts || (echo \"❌ Bulk processing failed. Review logs for errors in the processing pipeline and ensure all dependencies are met.\" && exit 1)", "bulk-process:enhanced": "tsx scripts/bulk-process-recordings.ts || (echo \"❌ Enhanced bulk processing failed. Review logs for errors in the enhanced processing pipeline using direct service integration.\" && exit 1)", "monitor-progress": "tsx scripts/monitor-bulk-progress.ts || (echo \"❌ Progress monitoring failed. Check session tracking and database connectivity.\" && exit 1)", "monitor-progress:watch": "tsx scripts/monitor-bulk-progress.ts --watch || (echo \"❌ Progress monitoring in watch mode failed. Check session tracking and database connectivity.\" && exit 1)", "monitor-progress:session": "tsx scripts/monitor-bulk-progress.ts --session", "cleanup-sessions": "tsx scripts/cleanup-bulk-sessions.ts || (echo \"❌ Session cleanup failed. Review logs for database cleanup errors.\" && exit 1)", "cleanup-sessions:dry-run": "tsx scripts/cleanup-bulk-sessions.ts --dry-run --verbose || (echo \"❌ Session cleanup dry run failed. Check cleanup script and database access.\" && exit 1)", "cleanup-sessions:stale": "tsx scripts/cleanup-bulk-sessions.ts --cancel-stale --verbose || (echo \"❌ Stale session cleanup failed. Check session management and database access.\" && exit 1)", "deploy-schema": "tsx scripts/deploy-azure-schema.ts || (echo \"❌ Schema deployment failed. Verify database connection and permissions. Consult Azure SQL documentation for schema deployment issues.\" && exit 1)", "diagnose-azure": "node scripts/fix-azure-deployment.js || (echo \"❌ Azure diagnosis failed. Verify Azure CLI is installed and configured, and check network connectivity to Azure services.\" && exit 1)", "validate-system-health": "tsx scripts/validate-system-health.ts || (echo \"❌ System health validation failed. Review logs for specific component failures and ensure all services are running.\" && exit 1)", "download-for-turboscribe": "tsx scripts/download-for-turboscribe.ts", "import-turboscribe-results": "tsx scripts/import-turboscribe-results.ts", "identify-missing-transcriptions": "tsx scripts/identify-missing-transcriptions.ts", "regenerate-all-summaries": "tsx scripts/regenerate-all-summaries.ts", "analyze-recording-counts": "tsx scripts/analyze-recording-counts.ts", "remove-duplicates": "tsx scripts/remove-duplicates.ts", "download-final-batch": "tsx scripts/download-final-batch.ts", "migrate-to-postgres": "tsx scripts/migrate-to-postgres.ts", "orchestrate-validation": "tsx scripts/orchestrate-system-validation.ts || (echo \"❌ System validation orchestration failed. Review logs for errors in orchestrator script and ensure all system components are accessible.\" && exit 1)", "validate-env-precedence": "tsx scripts/validate-env-precedence.ts || (echo \"❌ Environment variable validation failed. Review .env files and system environment variables for conflicts or incorrect precedence.\" && exit 1)", "test:functional": "tsx scripts/test-functional-workflow.ts || (echo \"❌ Functional tests failed. Review logs for specific component failures and ensure all services are accessible.\" && exit 1)", "test:functional:verbose": "tsx scripts/test-functional-workflow.ts --verbose || (echo \"❌ Functional tests failed with detailed output. Review above for specific errors.\" && exit 1)", "test:functional:quick": "tsx scripts/test-functional-workflow.ts --quick || (echo \"❌ Quick functional tests failed. Check essential components.\" && exit 1)", "validate:system": "npm run test:functional && npm run test-endpoints && npm run validate-system-health || (echo \"❌ System validation failed. Review individual test outputs above.\" && exit 1)", "validate:system:quick": "npm run test:functional:quick && npm run test-endpoints-quick || (echo \"❌ Quick system validation failed. Check basic functionality.\" && exit 1)", "test:webusb": "jest tests/integration/webusb-workflow.test.ts || (echo \"❌ WebUSB integration tests failed. Check browser compatibility and USB device handling.\" && exit 1)", "test:voice-processing": "jest tests/integration/voice-processing.test.ts || (echo \"❌ Voice processing integration tests failed. Check OpenAI API and transcription pipeline.\" && exit 1)", "test:appointment-integration": "jest tests/integration/appointment-integration.test.ts || (echo \"❌ Appointment integration tests failed. Check Sikka API connectivity and database sync.\" && exit 1)", "test:integration": "jest tests/integration/ --passWithNoTests || (echo \"❌ Integration tests failed. Review logs for specific workflow failures.\" && exit 1)", "test:integration:watch": "jest tests/integration/ --watch || (echo \"❌ Integration test watch mode failed.\" && exit 1)", "system-check-full": "npm run validate:system && npm run test:integration || (echo \"❌ Full system check failed. Review all test outputs above for detailed diagnostics.\" && exit 1)", "system-check-quick": "npm run validate:system:quick && npm run test:webusb || (echo \"❌ Quick system check failed. Address critical issues identified above.\" && exit 1)", "test-database-connection": "tsx scripts/test-database-connection.ts || (echo \"❌ Database connection failed. Verify connection string and database server status.\" && exit 1)", "init-azure-postgres": "tsx scripts/init-azure-postgres.ts || (echo \"❌ Azure PostgreSQL initialization failed. Verify AZURE_POSTGRES_CONNECTION_STRING and other related environment variables.\" && exit 1)", "verify-azure-postgres": "tsx scripts/verify-azure-postgres.ts || (echo \"❌ Azure PostgreSQL verification failed. Check database connectivity and data integrity.\" && exit 1)", "setup-azure-postgres": "npm run init-azure-postgres && npm run verify-azure-postgres", "process-all-audio": "tsx scripts/process-all-audio-quick.ts || (echo \"❌ Audio processing failed. Check Azure storage and OpenAI API key.\" && exit 1)", "test-azure-postgres-endpoints": "tsx scripts/test-azure-postgres-endpoints.ts || (echo \"❌ Azure PostgreSQL endpoint tests failed. Verify Azure PostgreSQL connection and API route functionality.\" && exit 1)", "validate-azure-blob": "tsx scripts/validate-azure-blob-integration.ts || exit 1", "validate-system-complete": "tsx scripts/comprehensive-system-validation.ts || exit 1", "azure-postgres-transition": "npm run test-azure-postgres-endpoints && npm run validate-azure-blob && npm run validate-system-complete", "azure-postgres-transition:quick": "npm run debug-env && npm run test-azure-postgres-endpoints && npm run validate-azure-blob || (echo \"❌ Quick Azure PostgreSQL transition check failed. Review logs for specific database and storage errors, and ensure critical issues are resolved.\" && exit 1)", "validate-environment-config": "tsx scripts/validate-environment-aware-config.ts || (echo \"❌ Environment-aware configuration validation failed. Review environment setup, feature flags, and their precedence rules.\" && exit 1)", "test-enhanced-feature-flags": "tsx scripts/test-enhanced-feature-flags.ts || (echo \"❌ Enhanced feature flag tests failed. Review feature flag resolution logic and configuration.\" && exit 1)", "export-vercel-transcriptions": "tsx scripts/export-vercel-transcriptions.ts || (echo \"❌ Vercel transcription export failed. Check POSTGRES_URL and database connectivity.\" && exit 1)", "audit-azure-blob": "tsx scripts/audit-azure-blob-recordings.ts || (echo \"❌ Azure Blob audit failed. Check AZURE_STORAGE_CONNECTION_STRING and container access.\" && exit 1)", "check-existing-transcriptions": "tsx scripts/check-existing-transcriptions.ts || (echo \"❌ Database check failed. Verify database connections and credentials.\" && exit 1)", "locate-transcriptions": "npm run export-vercel-transcriptions && npm run audit-azure-blob", "deploy": "npm run deploy:direct", "deploy:direct": "node scripts/package-for-zipdeploy.mjs && tsx scripts/await-zipdeploy.ts", "deploy:package": "node scripts/package-for-zipdeploy.mjs", "deploy:monitor": "tsx scripts/await-zipdeploy.ts --monitor-only", "deploy:github": "echo 'Push to main branch to trigger GitHub Actions deployment. Ensure your GitHub repository is correctly configured with GitHub Actions.'", "deploy:validate": "node scripts/validate-deployment.js || (echo \"❌ Deployment validation failed. Review deployment logs for errors and ensure the deployed application is functioning correctly.\" && exit 1)", "deploy:test": "node scripts/test-deployment-package.js || exit 1", "keyvault:test": "tsx scripts/test-keyvault-connectivity.ts || (echo \"❌ KeyVault connectivity test failed. Verify Azure KeyVault configuration and access policies.\" && exit 1)", "keyvault:status": "curl -s https://dentalapp-cpcwgucdawhmb4hu.centralus-01.azurewebsites.net/api/deployment-info | jq .keyVault || (echo \"❌ KeyVault status check failed. Ensure 'jq' is installed (e.g., 'npm install -g jq') and the application endpoint is reachable.\" && exit 1)", "logs": "az webapp log tail --name dentalapp --resource-group dentalapp || (echo \"❌ Failed to retrieve logs. Ensure Azure CLI is installed and configured, and you have permissions to access webapp logs.\" && exit 1)", "validate-for-github": "npm run lint && npm run build || exit 1", "test-github-secrets": "node scripts/setup-github-secrets.js || (echo \"❌ GitHub secrets test failed. Verify your GitHub token and repository settings.\" && exit 1)", "deploy:status": "gh run list --limit 5 --workflow=main_dentalapp.yml || exit 1", "deploy:quick": "cross-env ZIPDEPLOY_TIMEOUT_MS=300000 ZIPDEPLOY_RETRIES=3 npm run deploy:direct", "deploy:standard": "cross-env ZIPDEPLOY_TIMEOUT_MS=600000 ZIPDEPLOY_RETRIES=3 npm run deploy:direct", "deploy:large": "cross-env ZIPDEPLOY_TIMEOUT_MS=900000 ZIPDEPLOY_RETRIES=3 npm run deploy:direct", "deploy:config": "echo \"Current ZIPDEPLOY_TIMEOUT_MS=${ZIPDEPLOY_TIMEOUT_MS:-600000}ms, ZIPDEPLOY_RETRIES=${ZIPDEPLOY_RETRIES:-3}\"", "package:analyze": "node scripts/package-for-zipdeploy.mjs --analyze", "package:optimize": "node scripts/package-for-zipdeploy.mjs --optimize", "package:size": "node scripts/package-for-zipdeploy.mjs --size"}, "dependencies": {"@azure/data-tables": "^13.3.1", "@azure/storage-blob": "^12.27.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.10", "@types/jszip": "^3.4.0", "@types/mssql": "^9.1.7", "@types/pg": "^8.15.4", "@types/yargs": "^17.0.33", "@vercel/speed-insights": "^1.2.0", "axios": "^1.10.0", "better-sqlite3": "^9.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "mssql": "^11.0.1", "next": "^15.4.4", "next-themes": "^0.4.6", "openai": "^4.104.0", "pg": "^8.16.3", "qs": "^6.14.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "request": "^2.88.2", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "yargs": "^18.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/better-sqlite3": "^7.6.8", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "form-data": "^4.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-fetch": "^2.7.0", "playwright-mcp": "^0.0.12", "playwright-mcp-server": "^1.0.0", "postcss": "^8.5.4", "puppeteer": "^24.14.0", "rimraf": "^5.0.0", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "5.8.3"}}